"use client"

import { useEffect, useState } from "react"
import { Search, Eye, Edit, ShoppingCart } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { withAd<PERSON>Auth, useAdminAuth } from "@/contexts/admin-auth-context"
import { formatVND, formatDate, VIETNAMESE_LABELS } from "@/lib/vietnamese"

interface OrderItem {
  quantity: number
  price: number
  product: {
    id: number
    name: string
    image: string
    category: {
      id: number
      name: string
      image: string
    }
  }
}

interface Order {
  id: number
  userId: string
  status: 'pending' | 'confirmed' | 'shipping' | 'completed' | 'cancelled'
  paymentStatus: 'pending' | 'success' | 'failed'
  createdAt: string
  receivedAt?: string
  delivery: {
    type: 'shipping' | 'pickup'
    alias?: string
    address?: string
    name?: string
    phone?: string
  }
  total: number
  note?: string
  items: OrderItem[]
}

function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedStatus, setSelectedStatus] = useState<string>("")
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState<string>("")
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  
  // Dialog states
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  
  // Form states
  const [formData, setFormData] = useState({
    status: "",
    paymentStatus: "",
    note: ""
  })
  const [formLoading, setFormLoading] = useState(false)

  const { hasPermission } = useAdminAuth()

  const canWrite = hasPermission('orders.write')

  const fetchOrders = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (selectedStatus) params.append('status', selectedStatus)
      if (selectedPaymentStatus) params.append('paymentStatus', selectedPaymentStatus)
      
      const response = await fetch(`/api/admin/orders?${params}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Không thể tải đơn hàng')
      }

      setOrders(data)
      setError("")
    } catch (error) {
      console.error('Error fetching orders:', error)
      setError(error instanceof Error ? error.message : 'Lỗi không xác định')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchOrders()
  }, [selectedStatus, selectedPaymentStatus])

  const handleUpdateOrder = async () => {
    if (!selectedOrder) return

    try {
      setFormLoading(true)
      const response = await fetch(`/api/admin/orders/${selectedOrder.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: formData.status || selectedOrder.status,
          paymentStatus: formData.paymentStatus || selectedOrder.paymentStatus,
          note: formData.note
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Không thể cập nhật đơn hàng')
      }

      setSuccess("Cập nhật đơn hàng thành công")
      setIsEditDialogOpen(false)
      setSelectedOrder(null)
      setFormData({ status: "", paymentStatus: "", note: "" })
      fetchOrders()
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Lỗi không xác định')
    } finally {
      setFormLoading(false)
    }
  }

  const openViewDialog = (order: Order) => {
    setSelectedOrder(order)
    setIsViewDialogOpen(true)
  }

  const openEditDialog = (order: Order) => {
    setSelectedOrder(order)
    setFormData({
      status: order.status,
      paymentStatus: order.paymentStatus,
      note: order.note || ""
    })
    setIsEditDialogOpen(true)
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: "secondary" as const, label: VIETNAMESE_LABELS.orderStatus.pending },
      confirmed: { variant: "default" as const, label: VIETNAMESE_LABELS.orderStatus.confirmed },
      shipping: { variant: "default" as const, label: VIETNAMESE_LABELS.orderStatus.shipping },
      completed: { variant: "default" as const, label: VIETNAMESE_LABELS.orderStatus.completed },
      cancelled: { variant: "destructive" as const, label: VIETNAMESE_LABELS.orderStatus.cancelled }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig]
    return <Badge variant={config?.variant}>{config?.label || status}</Badge>
  }

  const getPaymentStatusBadge = (paymentStatus: string) => {
    const statusConfig = {
      pending: { variant: "secondary" as const, label: VIETNAMESE_LABELS.paymentStatus.pending },
      success: { variant: "default" as const, label: VIETNAMESE_LABELS.paymentStatus.success },
      failed: { variant: "destructive" as const, label: VIETNAMESE_LABELS.paymentStatus.failed }
    }
    
    const config = statusConfig[paymentStatus as keyof typeof statusConfig]
    return <Badge variant={config?.variant}>{config?.label || paymentStatus}</Badge>
  }

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.id.toString().includes(searchTerm) ||
                         order.delivery.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.delivery.phone?.includes(searchTerm)
    return matchesSearch
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Quản lý đơn hàng</h1>
        <p className="text-muted-foreground">
          Quản lý và theo dõi tất cả đơn hàng trong hệ thống
        </p>
      </div>

      {/* Alerts */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      {success && (
        <Alert>
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Tìm kiếm và lọc</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 flex-1">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Tìm kiếm theo ID, tên, số điện thoại..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
            </div>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Tất cả trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Tất cả trạng thái</SelectItem>
                <SelectItem value="pending">Chờ xác nhận</SelectItem>
                <SelectItem value="confirmed">Đã xác nhận</SelectItem>
                <SelectItem value="shipping">Đang giao hàng</SelectItem>
                <SelectItem value="completed">Hoàn thành</SelectItem>
                <SelectItem value="cancelled">Đã hủy</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedPaymentStatus} onValueChange={setSelectedPaymentStatus}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Tất cả thanh toán" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Tất cả thanh toán</SelectItem>
                <SelectItem value="pending">Chờ thanh toán</SelectItem>
                <SelectItem value="success">Đã thanh toán</SelectItem>
                <SelectItem value="failed">Thanh toán thất bại</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách đơn hàng</CardTitle>
          <CardDescription>
            Tổng cộng {filteredOrders.length} đơn hàng
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Đang tải...</p>
            </div>
          ) : filteredOrders.length === 0 ? (
            <div className="text-center py-8">
              <ShoppingCart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {searchTerm || selectedStatus || selectedPaymentStatus ? "Không tìm thấy đơn hàng nào" : "Chưa có đơn hàng nào"}
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Khách hàng</TableHead>
                  <TableHead>Tổng tiền</TableHead>
                  <TableHead>Trạng thái</TableHead>
                  <TableHead>Thanh toán</TableHead>
                  <TableHead>Ngày tạo</TableHead>
                  <TableHead className="text-right">Hành động</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredOrders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell className="font-medium">#{order.id}</TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{order.delivery.name || 'N/A'}</p>
                        <p className="text-sm text-muted-foreground">{order.delivery.phone || 'N/A'}</p>
                      </div>
                    </TableCell>
                    <TableCell>{formatVND(order.total)}</TableCell>
                    <TableCell>{getStatusBadge(order.status)}</TableCell>
                    <TableCell>{getPaymentStatusBadge(order.paymentStatus)}</TableCell>
                    <TableCell>{formatDate(order.createdAt)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openViewDialog(order)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        {canWrite && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditDialog(order)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Chi tiết đơn hàng #{selectedOrder?.id}</DialogTitle>
            <DialogDescription>
              Thông tin chi tiết về đơn hàng
            </DialogDescription>
          </DialogHeader>
          {selectedOrder && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Trạng thái đơn hàng</Label>
                  <div className="mt-1">{getStatusBadge(selectedOrder.status)}</div>
                </div>
                <div>
                  <Label>Trạng thái thanh toán</Label>
                  <div className="mt-1">{getPaymentStatusBadge(selectedOrder.paymentStatus)}</div>
                </div>
                <div>
                  <Label>Ngày tạo</Label>
                  <p className="text-sm">{formatDate(selectedOrder.createdAt)}</p>
                </div>
                <div>
                  <Label>Tổng tiền</Label>
                  <p className="text-sm font-medium">{formatVND(selectedOrder.total)}</p>
                </div>
              </div>
              
              <div>
                <Label>Thông tin giao hàng</Label>
                <div className="mt-1 p-3 bg-muted rounded-md">
                  <p><strong>Tên:</strong> {selectedOrder.delivery.name || 'N/A'}</p>
                  <p><strong>Số điện thoại:</strong> {selectedOrder.delivery.phone || 'N/A'}</p>
                  <p><strong>Địa chỉ:</strong> {selectedOrder.delivery.address || 'N/A'}</p>
                  <p><strong>Loại:</strong> {selectedOrder.delivery.type === 'shipping' ? 'Giao hàng' : 'Lấy tại cửa hàng'}</p>
                </div>
              </div>

              {selectedOrder.note && (
                <div>
                  <Label>Ghi chú</Label>
                  <p className="text-sm mt-1">{selectedOrder.note}</p>
                </div>
              )}

              <div>
                <Label>Sản phẩm ({selectedOrder.items.length})</Label>
                <div className="mt-2 space-y-2">
                  {selectedOrder.items.map((item, index) => (
                    <div key={index} className="flex items-center space-x-3 p-2 border rounded">
                      <img
                        src={item.product.image}
                        alt={item.product.name}
                        className="h-10 w-10 rounded object-cover"
                      />
                      <div className="flex-1">
                        <p className="font-medium">{item.product.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {item.quantity} x {formatVND(item.price)}
                        </p>
                      </div>
                      <p className="font-medium">{formatVND(item.quantity * item.price)}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cập nhật đơn hàng #{selectedOrder?.id}</DialogTitle>
            <DialogDescription>
              Thay đổi trạng thái đơn hàng và ghi chú
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="status">Trạng thái đơn hàng</Label>
              <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Chờ xác nhận</SelectItem>
                  <SelectItem value="confirmed">Đã xác nhận</SelectItem>
                  <SelectItem value="shipping">Đang giao hàng</SelectItem>
                  <SelectItem value="completed">Hoàn thành</SelectItem>
                  <SelectItem value="cancelled">Đã hủy</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="paymentStatus">Trạng thái thanh toán</Label>
              <Select value={formData.paymentStatus} onValueChange={(value) => setFormData({ ...formData, paymentStatus: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn trạng thái thanh toán" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Chờ thanh toán</SelectItem>
                  <SelectItem value="success">Đã thanh toán</SelectItem>
                  <SelectItem value="failed">Thanh toán thất bại</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="note">Ghi chú</Label>
              <Textarea
                id="note"
                value={formData.note}
                onChange={(e) => setFormData({ ...formData, note: e.target.value })}
                placeholder="Nhập ghi chú cho đơn hàng"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
              disabled={formLoading}
            >
              Hủy
            </Button>
            <Button onClick={handleUpdateOrder} disabled={formLoading}>
              {formLoading ? "Đang cập nhật..." : "Cập nhật"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default withAdminAuth(OrdersPage, "orders.read")
