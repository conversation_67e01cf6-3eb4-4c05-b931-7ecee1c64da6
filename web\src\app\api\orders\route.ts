import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    // Get user from session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Vui lòng đăng nhập để xem đơn hàng' },
        { status: 401 }
      )
    }

    const { data: orders, error } = await supabase
      .from('orders')
      .select(`
        *,
        order_items (
          quantity,
          price,
          products (
            id,
            name,
            image,
            categories (
              id,
              name,
              image
            )
          )
        )
      `)
      .eq('user_id', session.user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching orders:', error)
      return NextResponse.json(
        { error: 'Không thể tải đơn hàng' },
        { status: 500 }
      )
    }

    // Transform data to match the expected format
    const transformedOrders = orders?.map(order => ({
      id: order.id,
      status: order.status,
      paymentStatus: order.payment_status,
      createdAt: order.created_at,
      receivedAt: order.received_at,
      items: order.order_items?.map((item: any) => ({
        product: {
          id: item.products.id,
          name: item.products.name,
          price: item.price,
          originalPrice: item.price, // Use order price as both
          image: item.products.image,
          category: item.products.categories,
          detail: '' // Not needed for order display
        },
        quantity: item.quantity
      })) || [],
      delivery: order.delivery,
      total: order.total,
      note: order.note
    }))

    return NextResponse.json(transformedOrders)
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get user from session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Vui lòng đăng nhập để đặt hàng' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { items, delivery, total, note } = body

    // Create order
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert({
        user_id: session.user.id,
        delivery,
        total,
        note,
        status: 'pending',
        payment_status: 'pending'
      })
      .select()
      .single()

    if (orderError) {
      console.error('Error creating order:', orderError)
      return NextResponse.json(
        { error: 'Không thể tạo đơn hàng' },
        { status: 500 }
      )
    }

    // Create order items
    const orderItems = items.map((item: any) => ({
      order_id: order.id,
      product_id: item.productId,
      quantity: item.quantity,
      price: item.price
    }))

    const { error: itemsError } = await supabase
      .from('order_items')
      .insert(orderItems)

    if (itemsError) {
      console.error('Error creating order items:', itemsError)
      // Rollback order creation
      await supabase.from('orders').delete().eq('id', order.id)
      return NextResponse.json(
        { error: 'Không thể tạo chi tiết đơn hàng' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      message: 'Đặt hàng thành công',
      orderId: order.id 
    })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}
