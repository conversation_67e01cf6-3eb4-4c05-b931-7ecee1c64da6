"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { 
  LayoutDashboard, 
  Package, 
  FolderOpen, 
  ShoppingCart, 
  Image, 
  MapPin, 
  Users, 
  Settings, 
  LogOut, 
  Menu,
  X,
  BarChart3,
  Shield
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { She<PERSON>, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { ThemeToggle } from "@/components/theme-toggle"
import { useAdminAuth } from "@/contexts/admin-auth-context"

interface AdminLayoutProps {
  children: React.ReactNode
}

interface NavItem {
  href: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  permission?: string
  badge?: string
}

const navItems: NavItem[] = [
  {
    href: "/admin",
    label: "Tổng quan",
    icon: LayoutDashboard,
    permission: "analytics.read"
  },
  {
    href: "/admin/products",
    label: "Sản phẩm",
    icon: Package,
    permission: "products.read"
  },
  {
    href: "/admin/categories",
    label: "Danh mục",
    icon: FolderOpen,
    permission: "categories.read"
  },
  {
    href: "/admin/orders",
    label: "Đơn hàng",
    icon: ShoppingCart,
    permission: "orders.read"
  },
  {
    href: "/admin/banners",
    label: "Banner",
    icon: Image,
    permission: "banners.read"
  },
  {
    href: "/admin/stations",
    label: "Điểm lấy hàng",
    icon: MapPin,
    permission: "stations.read"
  },
  {
    href: "/admin/users",
    label: "Người dùng",
    icon: Users,
    permission: "users.read"
  },
  {
    href: "/admin/analytics",
    label: "Thống kê",
    icon: BarChart3,
    permission: "analytics.read"
  },
  {
    href: "/admin/settings",
    label: "Cài đặt",
    icon: Settings,
    permission: "settings.read"
  }
]

function AdminSidebar({ className = "" }: { className?: string }) {
  const pathname = usePathname()
  const { user, hasPermission } = useAdminAuth()

  const filteredNavItems = navItems.filter(item => 
    !item.permission || hasPermission(item.permission)
  )

  return (
    <div className={`flex flex-col h-full bg-card border-r ${className}`}>
      {/* Logo */}
      <div className="p-6 border-b">
        <Link href="/admin" className="flex items-center space-x-2">
          <Shield className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-xl font-bold">SachMart</h1>
            <p className="text-sm text-muted-foreground">Quản trị</p>
          </div>
        </Link>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {filteredNavItems.map((item) => {
          const isActive = pathname === item.href || 
            (item.href !== "/admin" && pathname.startsWith(item.href))
          
          return (
            <Link
              key={item.href}
              href={item.href}
              className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                isActive
                  ? "bg-primary text-primary-foreground"
                  : "text-muted-foreground hover:text-foreground hover:bg-muted"
              }`}
            >
              <item.icon className="h-4 w-4" />
              <span>{item.label}</span>
              {item.badge && (
                <Badge variant="secondary" className="ml-auto">
                  {item.badge}
                </Badge>
              )}
            </Link>
          )
        })}
      </nav>

      {/* User info */}
      <div className="p-4 border-t">
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarFallback>
              {user?.email?.charAt(0).toUpperCase() || "A"}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">{user?.email}</p>
            <p className="text-xs text-muted-foreground capitalize">
              {user?.role_name?.replace('_', ' ')}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

function AdminHeader() {
  const { user, signOut } = useAdminAuth()
  const router = useRouter()

  const handleSignOut = async () => {
    await signOut()
    router.push("/admin/login")
  }

  return (
    <header className="h-16 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex items-center justify-between h-full px-6">
        {/* Mobile menu trigger */}
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon" className="lg:hidden">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Mở menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-80 p-0">
            <AdminSidebar />
          </SheetContent>
        </Sheet>

        {/* Page title - will be dynamic based on current page */}
        <div className="flex-1 lg:ml-0 ml-4">
          <h2 className="text-lg font-semibold">Quản trị hệ thống</h2>
        </div>

        {/* Header actions */}
        <div className="flex items-center space-x-4">
          <ThemeToggle />
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                <Avatar className="h-8 w-8">
                  <AvatarFallback>
                    {user?.email?.charAt(0).toUpperCase() || "A"}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <div className="flex items-center justify-start gap-2 p-2">
                <div className="flex flex-col space-y-1 leading-none">
                  <p className="font-medium">{user?.email}</p>
                  <p className="text-xs text-muted-foreground capitalize">
                    {user?.role_name?.replace('_', ' ')}
                  </p>
                </div>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSignOut}>
                <LogOut className="mr-2 h-4 w-4" />
                <span>Đăng xuất</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}

export function AdminLayout({ children }: AdminLayoutProps) {
  return (
    <div className="h-screen flex">
      {/* Desktop sidebar */}
      <aside className="hidden lg:flex lg:w-80 lg:flex-col">
        <AdminSidebar />
      </aside>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
  )
}
