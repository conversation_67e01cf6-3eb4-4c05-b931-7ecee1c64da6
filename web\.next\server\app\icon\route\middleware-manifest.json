{"sorted_middleware": ["/icon/route"], "middleware": {}, "instrumentation": null, "functions": {"/icon/route": {"files": ["server/middleware-build-manifest.js", "server/interception-route-rewrite-manifest.js", "server/server-reference-manifest.js", "server/app/icon/route_client-reference-manifest.js", "server/edge/chunks/_next-internal_server_app_icon_route_actions_6c46f2f4.js", "server/edge/chunks/turbopack-_next-internal_server_app_icon_route_actions_7a96774c.js", "server/edge/chunks/node_modules_next_dist_esm_1f6c464f._.js", "server/edge/chunks/node_modules_next_dist_compiled_react-server-dom-turbopack_1d71ac1e._.js", "server/edge/chunks/node_modules_next_dist_compiled_@vercel_og_16be1ace._.js", "server/edge/chunks/node_modules_next_dist_compiled_8f4fd302._.js", "server/edge/chunks/node_modules_next_dist_7bea040f._.js", "server/edge/chunks/edge-wrapper_98c2e09e.js", "server/edge/chunks/[root-of-the-server]__bdb9cc89._.js", "server/edge/chunks/turbopack-edge-wrapper_ea2f910e.js"], "name": "app/icon/route", "page": "/icon/route", "matchers": [{"regexp": "^/icon(?:/)?$", "originalSource": "/icon"}], "wasm": [{"name": "wasm_fdb56e4cc5936d29", "filePath": "server/edge/chunks/node_modules_next_dist_compiled_@vercel_og_resvg_23ace1ce.wasm"}, {"name": "wasm_1ccdc983b1fd9f87", "filePath": "server/edge/chunks/node_modules_next_dist_compiled_@vercel_og_yoga_23ace1ce.wasm"}], "assets": [{"name": "server/edge/assets/noto-sans-v27-latin-regular.bdfa676c.ttf", "filePath": "server/edge/assets/noto-sans-v27-latin-regular.bdfa676c.ttf"}], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "c2WolBBUCp1bU98WkAXZq3MjcLMNTBcJ1AL3PTJRHcI=", "__NEXT_PREVIEW_MODE_ID": "7bc48530acaa40135ba4d3a97338e47b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e6732120280a8748a215afcddd89f184deaa76364563393ad931c5cb6d096d77", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8569132ee5bb0cf9a297bb2695d496b54f15dcc5ed045015109783d909a2b853"}}}}