import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import fs from 'fs'
import path from 'path'

// Mock data fallback
function getMockProducts() {
  try {
    const mockProductsPath = path.join(process.cwd(), '..', 'api', 'mock-output', 'products.json')
    const mockCategoriesPath = path.join(process.cwd(), '..', 'api', 'mock-output', 'categories.json')

    const productsData = fs.readFileSync(mockProductsPath, 'utf8')
    const categoriesData = fs.readFileSync(mockCategoriesPath, 'utf8')

    const products = JSON.parse(productsData)
    const categories = JSON.parse(categoriesData)

    // Add category data to products
    return products.map((product: any) => ({
      ...product,
      category: categories.find((cat: any) => cat.id === product.categoryId)
    }))
  } catch (error) {
    console.error('Error loading mock data:', error)
    return []
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('categoryId')
    const search = searchParams.get('search')
    const limit = searchParams.get('limit')
    const offset = searchParams.get('offset')

    // Try Supabase first
    try {
      let query = supabase
        .from('products')
        .select(`
          *,
          categories (
            id,
            name,
            image
          )
        `)

      // Filter by category if provided
      if (categoryId) {
        query = query.eq('category_id', categoryId)
      }

      // Search by name if provided
      if (search) {
        query = query.ilike('name', `%${search}%`)
      }

      // Add pagination
      if (limit) {
        query = query.limit(parseInt(limit))
      }
      if (offset) {
        query = query.range(parseInt(offset), parseInt(offset) + (parseInt(limit || '10')) - 1)
      }

      // Order by id
      query = query.order('id')

      const { data: products, error } = await query

      if (!error && products) {
        // Transform data to match the expected format
        const transformedProducts = products.map(product => ({
          id: product.id,
          categoryId: product.category_id,
          name: product.name,
          price: product.price,
          originalPrice: product.original_price,
          image: product.image,
          detail: product.detail,
          category: product.categories
        }))

        return NextResponse.json(transformedProducts)
      }
    } catch (supabaseError) {
      console.log('Supabase not available, using mock data')
    }

    // Fallback to mock data
    let products = getMockProducts()

    // Apply filters to mock data
    if (categoryId) {
      products = products.filter((product: any) => product.categoryId === parseInt(categoryId))
    }

    if (search) {
      products = products.filter((product: any) =>
        product.name.toLowerCase().includes(search.toLowerCase())
      )
    }

    // Apply pagination to mock data
    const startIndex = offset ? parseInt(offset) : 0
    const limitNum = limit ? parseInt(limit) : products.length
    products = products.slice(startIndex, startIndex + limitNum)

    return NextResponse.json(products)
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}
