import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

// Helper function to check admin permissions
async function checkAdminPermission(permission: string) {
  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()

    if (sessionError || !session) {
      return { authorized: false, error: 'Unauthorized' }
    }

    const { data, error } = await supabase
      .rpc('check_admin_permission', { permission_name: permission })

    if (error) {
      console.error('Permission check error:', error)
      // For development: if function doesn't exist, allow access
      if (error.message?.includes('function') || error.code === '42883') {
        console.log('Admin functions not available, allowing access for development')
        return { authorized: true, error: null }
      }
      return { authorized: false, error: 'Permission check failed' }
    }

    return { authorized: data === true, error: null }
  } catch (error) {
    console.error('Auth check error:', error)
    return { authorized: false, error: 'Auth check failed' }
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin permission
    const { authorized, error } = await checkAdminPermission('categories.read')
    if (!authorized) {
      return NextResponse.json(
        { error: error || 'Không có quyền truy cập' },
        { status: 401 }
      )
    }

    const { data: category, error: fetchError } = await supabase
      .from('categories')
      .select('*')
      .eq('id', params.id)
      .single()

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Không tìm thấy danh mục' },
          { status: 404 }
        )
      }
      console.error('Error fetching category:', fetchError)
      return NextResponse.json(
        { error: 'Không thể tải danh mục' },
        { status: 500 }
      )
    }

    return NextResponse.json(category)
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin permission
    const { authorized, error } = await checkAdminPermission('categories.write')
    if (!authorized) {
      return NextResponse.json(
        { error: error || 'Không có quyền cập nhật danh mục' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { name, image } = body

    // Validation
    if (!name || !image) {
      return NextResponse.json(
        { error: 'Tên danh mục và hình ảnh là bắt buộc' },
        { status: 400 }
      )
    }

    if (name.length < 2 || name.length > 255) {
      return NextResponse.json(
        { error: 'Tên danh mục phải từ 2 đến 255 ký tự' },
        { status: 400 }
      )
    }

    // Check if category exists
    const { data: existingCategory } = await supabase
      .from('categories')
      .select('id')
      .eq('id', params.id)
      .single()

    if (!existingCategory) {
      return NextResponse.json(
        { error: 'Không tìm thấy danh mục' },
        { status: 404 }
      )
    }

    // Check if name already exists (excluding current category)
    const { data: duplicateCategory } = await supabase
      .from('categories')
      .select('id')
      .eq('name', name)
      .neq('id', params.id)
      .single()

    if (duplicateCategory) {
      return NextResponse.json(
        { error: 'Tên danh mục đã tồn tại' },
        { status: 400 }
      )
    }

    // Update category
    const { data: category, error: updateError } = await supabase
      .from('categories')
      .update({
        name: name.trim(),
        image: image.trim()
      })
      .eq('id', params.id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating category:', updateError)
      return NextResponse.json(
        { error: 'Không thể cập nhật danh mục' },
        { status: 500 }
      )
    }

    return NextResponse.json(category)
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin permission
    const { authorized, error } = await checkAdminPermission('categories.delete')
    if (!authorized) {
      return NextResponse.json(
        { error: error || 'Không có quyền xóa danh mục' },
        { status: 401 }
      )
    }

    // Check if category exists
    const { data: existingCategory } = await supabase
      .from('categories')
      .select('id')
      .eq('id', params.id)
      .single()

    if (!existingCategory) {
      return NextResponse.json(
        { error: 'Không tìm thấy danh mục' },
        { status: 404 }
      )
    }

    // Check if category has products
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id')
      .eq('category_id', params.id)
      .limit(1)

    if (productsError) {
      console.error('Error checking products:', productsError)
      return NextResponse.json(
        { error: 'Không thể kiểm tra sản phẩm' },
        { status: 500 }
      )
    }

    if (products && products.length > 0) {
      return NextResponse.json(
        { error: 'Không thể xóa danh mục có sản phẩm. Vui lòng xóa tất cả sản phẩm trước.' },
        { status: 400 }
      )
    }

    // Delete category
    const { error: deleteError } = await supabase
      .from('categories')
      .delete()
      .eq('id', params.id)

    if (deleteError) {
      console.error('Error deleting category:', deleteError)
      return NextResponse.json(
        { error: 'Không thể xóa danh mục' },
        { status: 500 }
      )
    }

    return NextResponse.json({ message: 'Xóa danh mục thành công' })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}
