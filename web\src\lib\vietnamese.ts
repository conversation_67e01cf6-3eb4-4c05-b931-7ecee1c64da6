// Vietnamese localization utilities

export const formatVND = (amount: number): string => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(amount)
}

export const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('vi-VN').format(num)
}

export const formatDate = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return new Intl.DateTimeFormat('vi-VN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(dateObj)
}

export const formatShortDate = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return new Intl.DateTimeFormat('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  }).format(dateObj)
}

// Vietnamese text constants
export const VIETNAMESE_LABELS = {
  // Navigation
  home: 'Trang chủ',
  categories: 'Danh mục',
  products: 'Sản phẩm',
  cart: 'Giỏ hàng',
  orders: 'Đơn hàng',
  profile: 'Tài khoản',
  
  // Authentication
  login: 'Đăng nhập',
  register: 'Đăng ký',
  logout: 'Đăng xuất',
  email: 'Email',
  password: 'Mật khẩu',
  confirmPassword: 'Xác nhận mật khẩu',
  forgotPassword: 'Quên mật khẩu?',
  
  // Product
  addToCart: 'Thêm vào giỏ',
  buyNow: 'Mua ngay',
  quantity: 'Số lượng',
  price: 'Giá',
  originalPrice: 'Giá gốc',
  discount: 'Giảm giá',
  inStock: 'Còn hàng',
  outOfStock: 'Hết hàng',
  
  // Cart
  cartEmpty: 'Giỏ hàng trống',
  removeFromCart: 'Xóa khỏi giỏ',
  updateQuantity: 'Cập nhật số lượng',
  subtotal: 'Tạm tính',
  total: 'Tổng cộng',
  checkout: 'Thanh toán',
  
  // Order status
  orderStatus: {
    pending: 'Chờ xác nhận',
    confirmed: 'Đã xác nhận',
    shipping: 'Đang giao hàng',
    completed: 'Hoàn thành',
    cancelled: 'Đã hủy',
  },
  
  // Payment status
  paymentStatus: {
    pending: 'Chờ thanh toán',
    success: 'Đã thanh toán',
    failed: 'Thanh toán thất bại',
  },
  
  // Delivery
  delivery: 'Giao hàng',
  pickup: 'Lấy tại cửa hàng',
  shippingAddress: 'Địa chỉ giao hàng',
  pickupStation: 'Điểm lấy hàng',
  
  // Common actions
  search: 'Tìm kiếm',
  filter: 'Lọc',
  sort: 'Sắp xếp',
  save: 'Lưu',
  cancel: 'Hủy',
  confirm: 'Xác nhận',
  edit: 'Chỉnh sửa',
  delete: 'Xóa',
  view: 'Xem',
  back: 'Quay lại',
  next: 'Tiếp theo',
  previous: 'Trước',
  
  // Messages
  success: 'Thành công',
  error: 'Lỗi',
  warning: 'Cảnh báo',
  info: 'Thông tin',
  loading: 'Đang tải...',
  noResults: 'Không có kết quả',
  
  // Form validation
  required: 'Trường này là bắt buộc',
  invalidEmail: 'Email không hợp lệ',
  passwordTooShort: 'Mật khẩu phải có ít nhất 6 ký tự',
  passwordMismatch: 'Mật khẩu không khớp',
  
  // Theme
  theme: {
    light: 'Sáng',
    dark: 'Tối',
    system: 'Hệ thống',
    toggle: 'Chuyển đổi giao diện',
  },
} as const

export type VietnameseLabels = typeof VIETNAMESE_LABELS
