# SachMart - Vietnamese E-commerce Platform

SachMart là một ứng dụng thương mại điện tử hoàn chỉnh được xây dựng với Next.js 14+ và được thiết kế đặc biệt cho thị trường Việt Nam với giao diện hoàn toàn bằng tiếng Việt.

## 🚀 Tính năng chính

### 🛍️ Mua sắm
- **Trang chủ**: Banner carousel, danh mục sản phẩm, sản phẩm nổi bật
- **Danh mục sản phẩm**: Duyệt sản phẩm theo danh mục với bộ lọc
- **Tìm kiếm sản phẩm**: Tìm kiếm thông minh với từ khóa tiếng Việt
- **Chi tiết sản phẩm**: Thông tin chi tiết, h<PERSON><PERSON>nh, g<PERSON><PERSON> cả
- **Giỏ hàng**: Thêm/x<PERSON><PERSON> sả<PERSON> phẩm, cập nhật số lượng
- **Thanh toán**: Quy trình checkout hoàn chỉnh

### 👤 Quản lý tài khoản
- **Đăng ký/Đăng nhập**: Hệ thống xác thực người dùng
- **Hồ sơ cá nhân**: Quản lý thông tin tài khoản
- **Lịch sử đơn hàng**: Theo dõi trạng thái đơn hàng
- **Bảo mật**: Protected routes cho các trang yêu cầu đăng nhập

### 🎨 Giao diện
- **Responsive Design**: Tối ưu cho mobile, tablet, desktop
- **Dark/Light Theme**: Chuyển đổi chế độ sáng/tối
- **Vietnamese UI**: Hoàn toàn bằng tiếng Việt
- **Modern Design**: Sử dụng shadcn/ui components

### 💰 Thanh toán & Giao hàng
- **Định dạng tiền tệ VND**: Hiển thị giá theo chuẩn Việt Nam
- **Giao hàng tận nơi**: Nhập địa chỉ giao hàng
- **Lấy tại cửa hàng**: Chọn điểm lấy hàng
- **COD**: Thanh toán khi nhận hàng

## 🛠️ Công nghệ sử dụng

### Frontend
- **Next.js 14+**: React framework với App Router
- **TypeScript**: Type safety
- **Tailwind CSS**: Utility-first CSS framework
- **shadcn/ui**: Modern UI components
- **next-themes**: Theme management
- **Lucide React**: Icon library

### Backend & Database
- **Supabase**: Backend-as-a-Service (BaaS)
- **PostgreSQL**: Database
- **Row Level Security**: Bảo mật dữ liệu
- **Real-time subscriptions**: Cập nhật real-time

### State Management
- **React Context**: Global state management
- **Local Storage**: Persistent cart data
- **React Hooks**: Component state

## 📁 Cấu trúc dự án

```
sachmart/
├── api/                    # Mock data và API references
│   └── mock-output/       # Dữ liệu mẫu JSON
├── web/                   # Next.js application
│   ├── src/
│   │   ├── app/          # App Router pages
│   │   │   ├── api/      # API routes
│   │   │   ├── auth/     # Authentication pages
│   │   │   ├── cart/     # Shopping cart
│   │   │   ├── categories/ # Category pages
│   │   │   ├── checkout/ # Checkout process
│   │   │   ├── orders/   # Order management
│   │   │   ├── products/ # Product pages
│   │   │   └── profile/  # User profile
│   │   ├── components/   # Reusable components
│   │   │   ├── home/     # Homepage components
│   │   │   ├── layout/   # Layout components
│   │   │   ├── product/  # Product components
│   │   │   ├── theme-provider.tsx
│   │   │   └── ui/       # shadcn/ui components
│   │   ├── contexts/     # React contexts
│   │   │   ├── auth-context.tsx
│   │   │   └── cart-context.tsx
│   │   └── lib/          # Utilities
│   │       ├── supabase.ts
│   │       └── vietnamese.ts
│   ├── database/         # Database schema
│   └── public/          # Static assets
└── README.md
```

## 🚀 Cài đặt và chạy dự án

### Yêu cầu hệ thống
- Node.js 18+ 
- npm hoặc yarn
- Supabase account (tùy chọn)

### Cài đặt

1. **Clone repository**
```bash
git clone <repository-url>
cd sachmart
```

2. **Cài đặt dependencies**
```bash
cd web
npm install
```

3. **Cấu hình environment variables**
```bash
cp .env.local.example .env.local
```

Cập nhật file `.env.local` với thông tin Supabase của bạn:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

4. **Chạy development server**
```bash
npm run dev
```

Ứng dụng sẽ chạy tại `http://localhost:3000`

### Cấu hình Supabase (Tùy chọn)

1. Tạo project mới trên [Supabase](https://supabase.com)
2. Chạy SQL scripts trong thư mục `database/`
3. Cập nhật environment variables
4. Enable Row Level Security policies

## 📱 Tính năng responsive

Ứng dụng được thiết kế responsive cho tất cả các thiết bị:

- **Mobile (< 768px)**: Navigation drawer, stacked layout
- **Tablet (768px - 1024px)**: Grid layout tối ưu
- **Desktop (> 1024px)**: Full layout với sidebar

## 🌍 Localization

Toàn bộ ứng dụng sử dụng tiếng Việt:

- **UI Labels**: Tất cả nhãn giao diện
- **Error Messages**: Thông báo lỗi
- **Currency**: Định dạng VND (1.000.000 VND)
- **Date Format**: Định dạng ngày Việt Nam
- **Product Content**: Tên và mô tả sản phẩm

## 🔒 Bảo mật

- **Authentication**: Xác thực người dùng với Supabase Auth
- **Protected Routes**: Bảo vệ các trang yêu cầu đăng nhập
- **Row Level Security**: Bảo mật dữ liệu ở cấp database
- **Input Validation**: Kiểm tra dữ liệu đầu vào
- **XSS Protection**: Bảo vệ khỏi tấn công XSS

## 🧪 Testing

Để test ứng dụng:

1. **Chạy development server**
```bash
npm run dev
```

2. **Test các tính năng chính**:
   - Duyệt sản phẩm
   - Thêm vào giỏ hàng
   - Đăng ký/đăng nhập
   - Quy trình checkout
   - Responsive design

3. **Test API endpoints**:
```bash
curl http://localhost:3000/api/products
curl http://localhost:3000/api/categories
curl http://localhost:3000/api/banners
```

## 🚀 Deployment

### Vercel (Recommended)
```bash
npm run build
vercel --prod
```

### Docker
```bash
docker build -t sachmart .
docker run -p 3000:3000 sachmart
```

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📄 License

Distributed under the MIT License. See `LICENSE` for more information.

## 📞 Liên hệ

- **Project Link**: [https://github.com/your-username/sachmart](https://github.com/your-username/sachmart)
- **Demo**: [https://sachmart.vercel.app](https://sachmart.vercel.app)

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/)
- [Supabase](https://supabase.com/)
- [shadcn/ui](https://ui.shadcn.com/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Lucide Icons](https://lucide.dev/)
