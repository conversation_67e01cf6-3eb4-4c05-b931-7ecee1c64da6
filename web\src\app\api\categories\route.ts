import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import fs from 'fs'
import path from 'path'

// Mock data fallback
function getMockCategories() {
  try {
    const mockCategoriesPath = path.join(process.cwd(), '..', 'api', 'mock-output', 'categories.json')
    const categoriesData = fs.readFileSync(mockCategoriesPath, 'utf8')
    return JSON.parse(categoriesData)
  } catch (error) {
    console.error('Error loading mock categories:', error)
    return []
  }
}

export async function GET() {
  try {
    // Try Supabase first
    try {
      const { data: categories, error } = await supabase
        .from('categories')
        .select('*')
        .order('id')

      if (!error && categories) {
        return NextResponse.json(categories)
      }
    } catch (supabaseError) {
      console.log('Supabase not available, using mock data')
    }

    // Fallback to mock data
    const categories = getMockCategories()
    return NextResponse.json(categories)
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}
