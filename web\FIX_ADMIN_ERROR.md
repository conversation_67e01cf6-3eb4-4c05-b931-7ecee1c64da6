# 🚨 Fix "Database error granting user" - Step by Step

## Quick Fix Guide

### Step 1: Run Diagnostic Script
1. Open Supabase Dashboard → SQL Editor
2. Copy and paste the content of `database/diagnose-admin.sql`
3. Click "Run" to see what's missing

### Step 2: Fix Missing Schema (Most Common Issue)
If diagnostic shows missing tables/functions:

1. **Copy the entire content** of `database/admin-schema.sql`
2. **Paste it into Supabase SQL Editor**
3. **Click "Run"** - this will create all admin tables, functions, and policies
4. **Wait for completion** (should take a few seconds)

### Step 3: Create Your Admin User

#### Option A: Make Current User Admin (Easiest)
```sql
-- Run this in Supabase SQL Editor while logged in as the user you want to make admin
INSERT INTO admin_users (id, role_id, is_active, created_at) 
VALUES (
  auth.uid(),
  (SELECT id FROM admin_roles WHERE name = 'super_admin'),
  true,
  NOW()
)
ON CONFLICT (id) DO UPDATE SET
  role_id = (SELECT id FROM admin_roles WHERE name = 'super_admin'),
  is_active = true;
```

#### Option B: Make Specific User Admin by Email
```sql
-- Replace '<EMAIL>' with the actual email
INSERT INTO admin_users (id, role_id, is_active, created_at) 
SELECT 
  u.id,
  (SELECT id FROM admin_roles WHERE name = 'super_admin'),
  true,
  NOW()
FROM auth.users u 
WHERE u.email = '<EMAIL>'
ON CONFLICT (id) DO UPDATE SET
  role_id = (SELECT id FROM admin_roles WHERE name = 'super_admin'),
  is_active = true;
```

#### Option C: Make User Admin by ID
```sql
-- First, find the user ID:
SELECT id, email FROM auth.users WHERE email = '<EMAIL>';

-- Then use the ID:
INSERT INTO admin_users (id, role_id, is_active, created_at) 
VALUES (
  'paste-user-id-here',  -- Replace with actual UUID
  (SELECT id FROM admin_roles WHERE name = 'super_admin'),
  true,
  NOW()
);
```

### Step 4: Verify Fix
Run this to confirm everything works:
```sql
-- Check admin user was created
SELECT 
  au.id,
  u.email,
  ar.name as role_name,
  au.is_active
FROM admin_users au
JOIN auth.users u ON au.id = u.id
JOIN admin_roles ar ON au.role_id = ar.id;

-- Test permission function
SELECT check_admin_permission('categories.read');
```

### Step 5: Test Admin Login
1. Go to `http://localhost:3000/admin/login`
2. Login with the email/password of the user you made admin
3. You should now access the admin dashboard

## Common Error Messages & Solutions

### "relation 'admin_users' does not exist"
**Solution**: Run the complete `database/admin-schema.sql` file

### "function check_admin_permission does not exist"
**Solution**: Run the complete `database/admin-schema.sql` file

### "permission denied for table admin_users"
**Solution**: The user isn't in the admin_users table. Follow Step 3 above.

### "User not found in admin system"
**Solution**: The user exists in auth.users but not in admin_users. Follow Step 3 above.

## Emergency Reset (Nuclear Option)

If nothing works, run this to completely reset admin system:

```sql
-- WARNING: This will delete all admin users and recreate the system
DROP TABLE IF EXISTS admin_users CASCADE;
DROP TABLE IF EXISTS admin_roles CASCADE;
DROP FUNCTION IF EXISTS check_admin_permission(text);
DROP FUNCTION IF EXISTS get_admin_user_info();

-- Then run the complete database/admin-schema.sql file
-- Then follow Step 3 to create your admin user
```

## Still Having Issues?

1. **Check Supabase Connection**: Ensure your environment variables are correct
2. **Check Browser Console**: Look for JavaScript errors
3. **Check Network Tab**: Look for failed API requests
4. **Verify User Exists**: Make sure the user exists in `auth.users` table
5. **Check RLS Policies**: Ensure Row Level Security policies are working

## Environment Variables Check

Make sure these are set in your `.env.local`:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Development Mode

The admin system has fallback mechanisms for development:
- If admin functions don't exist, it allows access
- Mock permissions are used when database functions fail
- This helps during development but shouldn't be relied on in production

## Success Indicators

You'll know it's working when:
- ✅ You can access `/admin/login` without errors
- ✅ You can login and see the admin dashboard
- ✅ You can navigate to Categories, Products, Orders pages
- ✅ No console errors about missing functions or permissions

## Need More Help?

1. Run the diagnostic script first: `database/diagnose-admin.sql`
2. Check the detailed troubleshooting in `ADMIN_SETUP.md`
3. Verify all files are in place and Supabase is properly configured
