import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

// Helper function to check admin permissions
async function checkAdminPermission(permission: string) {
  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return { authorized: false, error: 'Unauthorized' }
    }

    const { data, error } = await supabase
      .rpc('check_admin_permission', { permission_name: permission })

    if (error) {
      console.error('Permission check error:', error)
      // For development: if function doesn't exist, allow access
      if (error.message?.includes('function') || error.code === '42883') {
        console.log('Admin functions not available, allowing access for development')
        return { authorized: true, error: null }
      }
      return { authorized: false, error: 'Permission check failed' }
    }

    return { authorized: data === true, error: null }
  } catch (error) {
    console.error('Auth check error:', error)
    return { authorized: false, error: 'Auth check failed' }
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin permission
    const { authorized, error } = await checkAdminPermission('products.read')
    if (!authorized) {
      return NextResponse.json(
        { error: error || 'Không có quyền truy cập' },
        { status: 401 }
      )
    }

    const { data: product, error: fetchError } = await supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          image
        )
      `)
      .eq('id', params.id)
      .single()

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Không tìm thấy sản phẩm' },
          { status: 404 }
        )
      }
      console.error('Error fetching product:', fetchError)
      return NextResponse.json(
        { error: 'Không thể tải sản phẩm' },
        { status: 500 }
      )
    }

    // Transform response
    const transformedProduct = {
      id: product.id,
      categoryId: product.category_id,
      name: product.name,
      price: product.price,
      originalPrice: product.original_price,
      image: product.image,
      detail: product.detail,
      category: product.categories,
      created_at: product.created_at
    }

    return NextResponse.json(transformedProduct)
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin permission
    const { authorized, error } = await checkAdminPermission('products.write')
    if (!authorized) {
      return NextResponse.json(
        { error: error || 'Không có quyền cập nhật sản phẩm' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { name, categoryId, price, originalPrice, image, detail } = body

    // Validation
    if (!name || !categoryId || !price || !originalPrice || !image) {
      return NextResponse.json(
        { error: 'Tên, danh mục, giá, giá gốc và hình ảnh là bắt buộc' },
        { status: 400 }
      )
    }

    if (name.length < 2 || name.length > 255) {
      return NextResponse.json(
        { error: 'Tên sản phẩm phải từ 2 đến 255 ký tự' },
        { status: 400 }
      )
    }

    if (price <= 0 || originalPrice <= 0) {
      return NextResponse.json(
        { error: 'Giá phải lớn hơn 0' },
        { status: 400 }
      )
    }

    if (price > originalPrice) {
      return NextResponse.json(
        { error: 'Giá bán không thể lớn hơn giá gốc' },
        { status: 400 }
      )
    }

    // Check if product exists
    const { data: existingProduct } = await supabase
      .from('products')
      .select('id')
      .eq('id', params.id)
      .single()

    if (!existingProduct) {
      return NextResponse.json(
        { error: 'Không tìm thấy sản phẩm' },
        { status: 404 }
      )
    }

    // Check if category exists
    const { data: category } = await supabase
      .from('categories')
      .select('id')
      .eq('id', categoryId)
      .single()

    if (!category) {
      return NextResponse.json(
        { error: 'Danh mục không tồn tại' },
        { status: 400 }
      )
    }

    // Update product
    const { data: product, error: updateError } = await supabase
      .from('products')
      .update({
        name: name.trim(),
        category_id: categoryId,
        price: parseInt(price),
        original_price: parseInt(originalPrice),
        image: image.trim(),
        detail: detail?.trim() || ''
      })
      .eq('id', params.id)
      .select(`
        *,
        categories (
          id,
          name,
          image
        )
      `)
      .single()

    if (updateError) {
      console.error('Error updating product:', updateError)
      return NextResponse.json(
        { error: 'Không thể cập nhật sản phẩm' },
        { status: 500 }
      )
    }

    // Transform response
    const transformedProduct = {
      id: product.id,
      categoryId: product.category_id,
      name: product.name,
      price: product.price,
      originalPrice: product.original_price,
      image: product.image,
      detail: product.detail,
      category: product.categories,
      created_at: product.created_at
    }

    return NextResponse.json(transformedProduct)
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin permission
    const { authorized, error } = await checkAdminPermission('products.delete')
    if (!authorized) {
      return NextResponse.json(
        { error: error || 'Không có quyền xóa sản phẩm' },
        { status: 401 }
      )
    }

    // Check if product exists
    const { data: existingProduct } = await supabase
      .from('products')
      .select('id')
      .eq('id', params.id)
      .single()

    if (!existingProduct) {
      return NextResponse.json(
        { error: 'Không tìm thấy sản phẩm' },
        { status: 404 }
      )
    }

    // Check if product is in any orders
    const { data: orderItems, error: orderItemsError } = await supabase
      .from('order_items')
      .select('id')
      .eq('product_id', params.id)
      .limit(1)

    if (orderItemsError) {
      console.error('Error checking order items:', orderItemsError)
      return NextResponse.json(
        { error: 'Không thể kiểm tra đơn hàng' },
        { status: 500 }
      )
    }

    if (orderItems && orderItems.length > 0) {
      return NextResponse.json(
        { error: 'Không thể xóa sản phẩm đã có trong đơn hàng' },
        { status: 400 }
      )
    }

    // Delete product
    const { error: deleteError } = await supabase
      .from('products')
      .delete()
      .eq('id', params.id)

    if (deleteError) {
      console.error('Error deleting product:', deleteError)
      return NextResponse.json(
        { error: 'Không thể xóa sản phẩm' },
        { status: 500 }
      )
    }

    return NextResponse.json({ message: 'Xóa sản phẩm thành công' })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}
