"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'

// Mock user interface for development
interface MockUser {
  id: string
  email: string
  created_at: string
  email_confirmed_at?: string
}

interface AuthContextType {
  user: MockUser | null
  session: any | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error: any }>
  signUp: (email: string, password: string) => Promise<{ error: any }>
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | null>(null)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<MockUser | null>(null)
  const [session, setSession] = useState<any | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Check for stored user in localStorage (mock implementation)
    const storedUser = localStorage.getItem('mock_user')
    if (storedUser) {
      const userData = JSON.parse(storedUser)
      setUser(userData)
      setSession({ user: userData })
    }
    setLoading(false)
  }, [])

  const signIn = async (email: string, password: string) => {
    // Mock sign in - for demo purposes
    if (email && password) {
      const mockUser: MockUser = {
        id: '1',
        email: email,
        created_at: new Date().toISOString(),
        email_confirmed_at: new Date().toISOString()
      }
      setUser(mockUser)
      setSession({ user: mockUser })
      localStorage.setItem('mock_user', JSON.stringify(mockUser))
      return { error: null }
    }
    return { error: { message: 'Invalid login credentials' } }
  }

  const signUp = async (email: string, password: string) => {
    // Mock sign up - for demo purposes
    if (email && password.length >= 6) {
      const mockUser: MockUser = {
        id: '1',
        email: email,
        created_at: new Date().toISOString(),
        email_confirmed_at: new Date().toISOString()
      }
      setUser(mockUser)
      setSession({ user: mockUser })
      localStorage.setItem('mock_user', JSON.stringify(mockUser))
      return { error: null }
    }
    return { error: { message: 'Password should be at least 6 characters' } }
  }

  const signOut = async () => {
    setUser(null)
    setSession(null)
    localStorage.removeItem('mock_user')
  }

  return (
    <AuthContext.Provider value={{
      user,
      session,
      loading,
      signIn,
      signUp,
      signOut,
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
