# Hướng dẫn thiết lập Supabase cho SachMart

## Bước 1: Tạo dự án Supabase mới

1. <PERSON><PERSON><PERSON> cập [https://supabase.com](https://supabase.com)
2. <PERSON><PERSON><PERSON> nhập hoặc tạo tài khoản mới
3. <PERSON><PERSON>ấp "New Project"
4. Chọn organization và nhập thông tin dự án:
   - **Name**: SachMart
   - **Database Password**: Tạo mật khẩu mạnh
   - **Region**: Southeast Asia (Singapore) - gần Việt Nam nhất
5. Nhấp "Create new project"

## Bước 2: Cấu hình Database Schema

1. Trong dashboard Supabase, vào **SQL Editor**
2. Tạo query mới và copy toàn bộ nội dung từ file `database/schema.sql`
3. Chạy query để tạo tables và policies
4. Tạo query mới và copy nội dung từ file `database/sample-products.sql`
5. <PERSON><PERSON><PERSON> query để thêm dữ liệu mẫu

## Bước 3: <PERSON><PERSON>u hình Authentication

1. Vào **Authentication** > **Settings**
2. Trong **Site URL**, thêm: `http://localhost:3000`
3. Trong **Redirect URLs**, thêm:
   - `http://localhost:3000/auth/callback`
   - `http://localhost:3000`
4. Bật **Enable email confirmations** nếu muốn xác thực email

## Bước 4: Lấy API Keys

1. Vào **Settings** > **API**
2. Copy các giá trị sau:
   - **Project URL**
   - **anon public key**
   - **service_role key** (chỉ dùng cho server-side)

## Bước 5: Cấu hình Environment Variables

1. Mở file `.env.local` trong thư mục web
2. Thay thế các giá trị placeholder:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
```

## Bước 6: Kiểm tra kết nối

1. Chạy ứng dụng: `npm run dev`
2. Mở browser và truy cập `http://localhost:3000`
3. Kiểm tra console để đảm bảo không có lỗi kết nối

## Cấu trúc Database

### Tables chính:
- **categories**: Danh mục sản phẩm
- **products**: Sản phẩm
- **banners**: Banner quảng cáo
- **stations**: Điểm lấy hàng
- **orders**: Đơn hàng
- **order_items**: Chi tiết đơn hàng

### Row Level Security (RLS):
- Tất cả tables đều bật RLS
- Categories, products, banners, stations: Public read access
- Orders và order_items: Chỉ user sở hữu mới xem được

## Lưu ý bảo mật

- **KHÔNG** commit file `.env.local` vào git
- **service_role key** có quyền admin, chỉ dùng cho server-side operations
- **anon key** an toàn để expose ở client-side
- RLS policies đảm bảo data security

## Troubleshooting

### Lỗi kết nối:
- Kiểm tra URL và API keys
- Đảm bảo project Supabase đã được tạo thành công

### Lỗi authentication:
- Kiểm tra redirect URLs trong Supabase dashboard
- Đảm bảo Site URL được cấu hình đúng

### Lỗi database:
- Kiểm tra RLS policies
- Đảm bảo schema đã được tạo đúng
