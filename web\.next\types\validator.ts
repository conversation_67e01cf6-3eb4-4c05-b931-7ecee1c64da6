// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/types.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
}


// Validate ../../src/app/admin/categories/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/admin/categories">> = Specific
  const handler = {} as typeof import("../../src/app/admin/categories/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/admin/login/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/admin/login">> = Specific
  const handler = {} as typeof import("../../src/app/admin/login/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/admin/orders/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/admin/orders">> = Specific
  const handler = {} as typeof import("../../src/app/admin/orders/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/admin/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/admin">> = Specific
  const handler = {} as typeof import("../../src/app/admin/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/admin/products/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/admin/products">> = Specific
  const handler = {} as typeof import("../../src/app/admin/products/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/auth/login/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/auth/login">> = Specific
  const handler = {} as typeof import("../../src/app/auth/login/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/auth/register/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/auth/register">> = Specific
  const handler = {} as typeof import("../../src/app/auth/register/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/cart/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/cart">> = Specific
  const handler = {} as typeof import("../../src/app/cart/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/categories/[id]/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/categories/[id]">> = Specific
  const handler = {} as typeof import("../../src/app/categories/[id]/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/categories/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/categories">> = Specific
  const handler = {} as typeof import("../../src/app/categories/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/checkout/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/checkout">> = Specific
  const handler = {} as typeof import("../../src/app/checkout/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/orders/[id]/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/orders/[id]">> = Specific
  const handler = {} as typeof import("../../src/app/orders/[id]/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/orders/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/orders">> = Specific
  const handler = {} as typeof import("../../src/app/orders/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/">> = Specific
  const handler = {} as typeof import("../../src/app/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/products/[id]/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/products/[id]">> = Specific
  const handler = {} as typeof import("../../src/app/products/[id]/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/products/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/products">> = Specific
  const handler = {} as typeof import("../../src/app/products/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/profile/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/profile">> = Specific
  const handler = {} as typeof import("../../src/app/profile/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/admin/categories/[id]/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/admin/categories/[id]">> = Specific
  const handler = {} as typeof import("../../src/app/api/admin/categories/[id]/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/admin/categories/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/admin/categories">> = Specific
  const handler = {} as typeof import("../../src/app/api/admin/categories/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/admin/orders/[id]/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/admin/orders/[id]">> = Specific
  const handler = {} as typeof import("../../src/app/api/admin/orders/[id]/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/admin/orders/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/admin/orders">> = Specific
  const handler = {} as typeof import("../../src/app/api/admin/orders/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/admin/products/[id]/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/admin/products/[id]">> = Specific
  const handler = {} as typeof import("../../src/app/api/admin/products/[id]/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/admin/products/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/admin/products">> = Specific
  const handler = {} as typeof import("../../src/app/api/admin/products/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/banners/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/banners">> = Specific
  const handler = {} as typeof import("../../src/app/api/banners/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/categories/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/categories">> = Specific
  const handler = {} as typeof import("../../src/app/api/categories/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/orders/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/orders">> = Specific
  const handler = {} as typeof import("../../src/app/api/orders/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/products/[id]/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/products/[id]">> = Specific
  const handler = {} as typeof import("../../src/app/api/products/[id]/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/products/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/products">> = Specific
  const handler = {} as typeof import("../../src/app/api/products/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/stations/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/stations">> = Specific
  const handler = {} as typeof import("../../src/app/api/stations/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}





// Validate ../../src/app/admin/layout.tsx
{
  type __IsExpected<Specific extends LayoutConfig<"/admin">> = Specific
  const handler = {} as typeof import("../../src/app/admin/layout.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/layout.tsx
{
  type __IsExpected<Specific extends LayoutConfig<"/">> = Specific
  const handler = {} as typeof import("../../src/app/layout.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}
