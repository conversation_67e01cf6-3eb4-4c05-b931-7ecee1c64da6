"use client"

import { useEffect, useState } from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { CategoryGrid } from "@/components/home/<USER>"
import { Category } from "@/lib/supabase"

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories')
        if (response.ok) {
          const data = await response.json()
          setCategories(data)
        }
      } catch (error) {
        console.error('Error fetching categories:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchCategories()
  }, [])

  return (
    <MainLayout>
      <div className="py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4"><PERSON><PERSON> mụ<PERSON> sản phẩm</h1>
          <p className="text-muted-foreground">
            <PERSON><PERSON><PERSON><PERSON> phá các danh mục sản phẩm đa dạng tại <PERSON>chM<PERSON>
          </p>
        </div>

        {loading ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">Đang tải...</p>
          </div>
        ) : (
          <CategoryGrid categories={categories} />
        )}
      </div>
    </MainLayout>
  )
}
