import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import fs from 'fs'
import path from 'path'

// Mock data fallback
function getMockStations() {
  try {
    const mockStationsPath = path.join(process.cwd(), '..', 'api', 'mock-output', 'stations.json')
    const stationsData = fs.readFileSync(mockStationsPath, 'utf8')
    return JSON.parse(stationsData)
  } catch (error) {
    console.error('Error loading mock stations:', error)
    return []
  }
}

export async function GET() {
  try {
    // Try Supabase first
    try {
      const { data: stations, error } = await supabase
        .from('stations')
        .select('*')
        .order('id')

      if (!error && stations) {
        return NextResponse.json(stations)
      }
    } catch (supabaseError) {
      console.log('Supabase not available, using mock data')
    }

    // Fallback to mock data
    const stations = getMockStations()
    return NextResponse.json(stations)
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}
