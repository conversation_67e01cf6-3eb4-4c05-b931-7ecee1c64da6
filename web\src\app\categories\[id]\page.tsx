"use client"

import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { MainLayout } from "@/components/layout/main-layout"
import { ProductCard } from "@/components/product/product-card"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { Category } from "@/lib/supabase"

interface Product {
  id: number
  categoryId: number
  name: string
  price: number
  originalPrice: number
  image: string
  detail?: string
  category?: Category
}

export default function CategoryPage() {
  const params = useParams()
  const categoryId = params.id as string
  const [category, setCategory] = useState<Category | null>(null)
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch category info
        const categoriesResponse = await fetch('/api/categories')
        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json()
          const foundCategory = categoriesData.find((c: Category) => c.id === parseInt(categoryId))
          setCategory(foundCategory || null)
        }

        // Fetch products in this category
        const productsResponse = await fetch(`/api/products?categoryId=${categoryId}`)
        if (productsResponse.ok) {
          const productsData = await productsResponse.json()
          setProducts(productsData)
        }
      } catch (error) {
        console.error('Error fetching data:', error)
      } finally {
        setLoading(false)
      }
    }

    if (categoryId) {
      fetchData()
    }
  }, [categoryId])



  if (loading) {
    return (
      <MainLayout>
        <div className="py-8">
          <div className="text-center">
            <p className="text-muted-foreground">Đang tải...</p>
          </div>
        </div>
      </MainLayout>
    )
  }

  if (!category) {
    return (
      <MainLayout>
        <div className="py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Không tìm thấy danh mục</h1>
            <Button asChild>
              <Link href="/categories">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Quay lại danh mục
              </Link>
            </Button>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="py-8">
        {/* Header */}
        <div className="mb-8">
          <Button variant="ghost" asChild className="mb-4">
            <Link href="/categories">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Quay lại danh mục
            </Link>
          </Button>
          
          <h1 className="text-3xl font-bold mb-4">{category.name}</h1>
          <p className="text-muted-foreground">
            Khám phá các sản phẩm trong danh mục {category.name}
          </p>
        </div>

        {/* Products Grid */}
        {products.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              Chưa có sản phẩm nào trong danh mục này
            </p>
          </div>
        ) : (
          <>
            <div className="mb-4">
              <p className="text-sm text-muted-foreground">
                Tìm thấy {products.length} sản phẩm
              </p>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {products.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                />
              ))}
            </div>
          </>
        )}
      </div>
    </MainLayout>
  )
}
