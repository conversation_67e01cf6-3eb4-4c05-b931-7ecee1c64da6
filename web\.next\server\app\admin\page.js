var R=require("../../chunks/ssr/[turbopack]_runtime.js")("server/app/admin/page.js")
R.c("server/chunks/ssr/node_modules_5f2d4120._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e8a2741f._.js")
R.c("server/chunks/ssr/src_app_ca777385._.js")
R.c("server/chunks/ssr/[externals]_next_dist_compiled_@vercel_og_index_node_84b3c02c.js")
R.c("server/chunks/ssr/_74829c23._.js")
R.c("server/chunks/ssr/src_app_c78746f0._.js")
R.c("server/chunks/ssr/[root-of-the-server]__ce5b206a._.js")
R.c("server/chunks/ssr/node_modules_next_dist_client_components_9774470f._.js")
R.c("server/chunks/ssr/node_modules_next_dist_client_components_builtin_forbidden_45780354.js")
R.c("server/chunks/ssr/node_modules_next_dist_client_components_builtin_unauthorized_15817684.js")
R.c("server/chunks/ssr/node_modules_next_dist_client_components_builtin_global-error_ece394eb.js")
R.c("server/chunks/ssr/src_app_admin_layout_tsx_07d69fb5._.js")
R.c("server/chunks/ssr/node_modules_next_dist_6f6375d3._.js")
R.c("server/chunks/ssr/[root-of-the-server]__ff1c4a2d._.js")
R.m("[project]/.next-internal/server/app/admin/page/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/admin/page { GLOBAL_ERROR_MODULE => \"[project]/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", METADATA_1 => \"[project]/src/app/icon--metadata.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_2 => \"[project]/src/app/apple-icon--metadata.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_7 => \"[project]/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_8 => \"[project]/src/app/admin/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_9 => \"[project]/src/app/admin/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/admin/page { GLOBAL_ERROR_MODULE => \"[project]/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", METADATA_1 => \"[project]/src/app/icon--metadata.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_2 => \"[project]/src/app/apple-icon--metadata.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_7 => \"[project]/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_8 => \"[project]/src/app/admin/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_9 => \"[project]/src/app/admin/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports
