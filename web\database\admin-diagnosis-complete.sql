-- COMPLETE ADMIN <NAME_EMAIL>
-- Copy and paste this entire script into Supabase SQL Editor and run it
-- This will diagnose and potentially fix the admin access issue

-- ============================================================================
-- 1. CHECK ADMIN DATABASE SCHEMA
-- ============================================================================

SELECT 
  '🔍 STEP 1: Admin Schema Check' as step,
  'Checking if admin tables exist...' as description;

SELECT 
  table_name,
  CASE 
    WHEN table_name = 'admin_roles' THEN '✅ admin_roles table exists'
    WHEN table_name = 'admin_users' THEN '✅ admin_users table exists'
    ELSE '❓ Unknown table'
  END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('admin_roles', 'admin_users')
ORDER BY table_name;

-- Check if we have the expected 2 tables
SELECT 
  CASE 
    WHEN COUNT(*) = 2 THEN '✅ PASS: Both admin tables exist'
    WHEN COUNT(*) = 1 THEN '⚠️  WARNING: Only one admin table exists - run admin-schema.sql'
    ELSE '❌ FAIL: No admin tables found - MUST run admin-schema.sql'
  END as schema_status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('admin_roles', 'admin_users');

-- ============================================================================
-- 2. VERIFY USER EXISTS IN auth.users
-- ============================================================================

SELECT 
  '🔍 STEP 2: User Existence Check' as step,
  '<NAME_EMAIL> exists in auth.users...' as description;

SELECT 
  id,
  email,
  created_at,
  last_sign_in_at,
  email_confirmed_at,
  CASE 
    WHEN email = '<EMAIL>' THEN '✅ User exists in auth.users'
    ELSE '❓ Different user found'
  END as status
FROM auth.users 
WHERE email = '<EMAIL>';

-- Check if user exists
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') 
    THEN '✅ PASS: <EMAIL> exists in auth.users'
    ELSE '❌ FAIL: <EMAIL> NOT found in auth.users'
  END as user_existence_status;

-- ============================================================================
-- 3. CHECK IF USER IS IN admin_users TABLE
-- ============================================================================

SELECT 
  '🔍 STEP 3: Admin User Check' as step,
  '<NAME_EMAIL> is in admin_users table...' as description;

-- This will only work if admin tables exist
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'admin_users') THEN
    PERFORM 1; -- Table exists, continue with query below
  ELSE
    RAISE NOTICE '❌ admin_users table does not exist - run admin-schema.sql first';
  END IF;
END $$;

-- Check admin user status (only if table exists)
SELECT 
  au.id,
  u.email,
  ar.name as role_name,
  au.is_active,
  au.created_at,
  CASE 
    WHEN au.is_active = true THEN '✅ User is active admin'
    WHEN au.is_active = false THEN '⚠️  User is inactive admin'
    ELSE '❓ Unknown status'
  END as admin_status
FROM admin_users au
JOIN auth.users u ON au.id = u.id
JOIN admin_roles ar ON au.role_id = ar.id
WHERE u.email = '<EMAIL>';

-- Summary of admin user status
SELECT 
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM admin_users au 
      JOIN auth.users u ON au.id = u.id 
      WHERE u.email = '<EMAIL>' AND au.is_active = true
    ) THEN '✅ PASS: <EMAIL> is active admin'
    WHEN EXISTS (
      SELECT 1 FROM admin_users au 
      JOIN auth.users u ON au.id = u.id 
      WHERE u.email = '<EMAIL>' AND au.is_active = false
    ) THEN '⚠️  WARNING: <EMAIL> is inactive admin'
    ELSE '❌ FAIL: <EMAIL> NOT in admin_users table'
  END as admin_user_status;

-- ============================================================================
-- 4. VERIFY ADMIN DATABASE FUNCTIONS
-- ============================================================================

SELECT 
  '🔍 STEP 4: Admin Functions Check' as step,
  'Checking if admin functions exist...' as description;

SELECT 
  routine_name,
  routine_type,
  CASE 
    WHEN routine_name = 'check_admin_permission' THEN '✅ Permission check function exists'
    WHEN routine_name = 'get_admin_user_info' THEN '✅ User info function exists'
    ELSE '❓ Other function'
  END as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('check_admin_permission', 'get_admin_user_info')
ORDER BY routine_name;

-- Check function count
SELECT 
  CASE 
    WHEN COUNT(*) >= 2 THEN '✅ PASS: Admin functions exist'
    WHEN COUNT(*) = 1 THEN '⚠️  WARNING: Only one admin function exists'
    ELSE '❌ FAIL: No admin functions found - run admin-schema.sql'
  END as functions_status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('check_admin_permission', 'get_admin_user_info');

-- ============================================================================
-- 5. TEST PERMISSION FUNCTIONS (if they exist)
-- ============================================================================

SELECT 
  '🔍 STEP 5: Function Testing' as step,
  'Testing admin functions...' as description;

-- Test permission function (will fail if function doesn't exist or user not admin)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'check_admin_permission') THEN
    RAISE NOTICE 'Testing check_admin_permission function...';
  ELSE
    RAISE NOTICE '❌ check_admin_permission function does not exist';
  END IF;
END $$;

-- Test get_admin_user_info function
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'get_admin_user_info') THEN
    RAISE NOTICE 'Testing get_admin_user_info function...';
  ELSE
    RAISE NOTICE '❌ get_admin_user_info function does not exist';
  END IF;
END $$;

-- ============================================================================
-- 6. SHOW ALL ADMIN ROLES (if table exists)
-- ============================================================================

SELECT 
  '🔍 STEP 6: Admin Roles Check' as step,
  'Showing available admin roles...' as description;

-- Show admin roles (only if table exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'admin_roles') THEN
    PERFORM 1; -- Continue with query below
  ELSE
    RAISE NOTICE '❌ admin_roles table does not exist';
  END IF;
END $$;

SELECT 
  id,
  name,
  description,
  jsonb_array_length(permissions) as permission_count
FROM admin_roles 
ORDER BY id;

-- ============================================================================
-- 7. DIAGNOSIS SUMMARY
-- ============================================================================

SELECT 
  '📋 DIAGNOSIS SUMMARY' as step,
  'Overall system status...' as description;

-- Overall diagnosis
SELECT 
  CASE 
    WHEN NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'admin_users')
    THEN '❌ CRITICAL: Admin schema missing - run admin-schema.sql'
    
    WHEN NOT EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>')
    THEN '❌ CRITICAL: User <EMAIL> does not exist in auth.users'
    
    WHEN NOT EXISTS (
      SELECT 1 FROM admin_users au 
      JOIN auth.users u ON au.id = u.id 
      WHERE u.email = '<EMAIL>'
    )
    THEN '❌ ISSUE FOUND: User exists but not in admin_users - needs to be added'
    
    WHEN EXISTS (
      SELECT 1 FROM admin_users au 
      JOIN auth.users u ON au.id = u.id 
      WHERE u.email = '<EMAIL>' AND au.is_active = false
    )
    THEN '⚠️  ISSUE FOUND: User is admin but inactive - needs activation'
    
    WHEN EXISTS (
      SELECT 1 FROM admin_users au 
      JOIN auth.users u ON au.id = u.id 
      WHERE u.email = '<EMAIL>' AND au.is_active = true
    )
    THEN '✅ USER IS ADMIN: Check application code or browser console for errors'
    
    ELSE '❓ UNKNOWN STATUS: Manual investigation needed'
  END as diagnosis;

-- ============================================================================
-- 8. AUTOMATIC FIX (if needed)
-- ============================================================================

SELECT 
  '🔧 STEP 8: Automatic Fix' as step,
  'Attempting to fix admin access...' as description;

-- Add user to admin_users if missing (only if tables exist and user exists)
DO $$
BEGIN
  -- Check if we can fix the issue
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'admin_users') 
     AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'admin_roles')
     AND EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>')
     AND NOT EXISTS (
       SELECT 1 FROM admin_users au 
       JOIN auth.users u ON au.id = u.id 
       WHERE u.email = '<EMAIL>'
     ) THEN
    
    -- Add user to admin_users
    INSERT INTO admin_users (id, role_id, is_active, created_at) 
    SELECT 
      u.id,
      (SELECT id FROM admin_roles WHERE name = 'super_admin'),
      true,
      NOW()
    FROM auth.users u 
    WHERE u.email = '<EMAIL>';
    
    RAISE NOTICE '✅ SUCCESS: Added <EMAIL> to admin_users with super_admin role';
    
  ELSIF EXISTS (
    SELECT 1 FROM admin_users au 
    JOIN auth.users u ON au.id = u.id 
    WHERE u.email = '<EMAIL>' AND au.is_active = false
  ) THEN
    
    -- Activate existing admin user
    UPDATE admin_users 
    SET is_active = true 
    WHERE id = (SELECT id FROM auth.users WHERE email = '<EMAIL>');
    
    RAISE NOTICE '✅ SUCCESS: Activated <EMAIL>';
    
  ELSE
    RAISE NOTICE 'ℹ️  INFO: No automatic fix applied - check diagnosis above';
  END IF;
END $$;

-- ============================================================================
-- 9. FINAL VERIFICATION
-- ============================================================================

SELECT 
  '✅ FINAL VERIFICATION' as step,
  '<NAME_EMAIL> can now access admin...' as description;

-- Final check
SELECT 
  au.id,
  u.email,
  ar.name as role_name,
  au.is_active,
  CASE 
    WHEN au.is_active = true THEN '✅ READY: User should be able to access admin interface'
    ELSE '❌ STILL BLOCKED: User cannot access admin interface'
  END as final_status
FROM admin_users au
JOIN auth.users u ON au.id = u.id
JOIN admin_roles ar ON au.role_id = ar.id
WHERE u.email = '<EMAIL>';

-- ============================================================================
-- MANUAL FIX COMMANDS (if automatic fix didn't work)
-- ============================================================================

SELECT 
  '📝 MANUAL FIX COMMANDS' as step,
  'Use these if automatic fix did not work...' as description;

-- Show manual fix command
SELECT 
  'If <EMAIL> is still not working, run this manually:' as instruction,
  '
-- Manual fix command:
INSERT INTO admin_users (id, role_id, is_active, created_at) 
SELECT 
  u.id,
  (SELECT id FROM admin_roles WHERE name = ''super_admin''),
  true,
  NOW()
FROM auth.users u 
WHERE u.email = ''<EMAIL>''
ON CONFLICT (id) DO UPDATE SET
  role_id = (SELECT id FROM admin_roles WHERE name = ''super_admin''),
  is_active = true;
  ' as manual_fix_sql;
