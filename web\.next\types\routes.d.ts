// This file is generated automatically by Next.js
// Do not edit this file manually

type AppRoutes = "/" | "/admin" | "/admin/categories" | "/admin/login" | "/admin/orders" | "/admin/products" | "/auth/login" | "/auth/register" | "/cart" | "/categories" | "/categories/[id]" | "/checkout" | "/orders" | "/orders/[id]" | "/products" | "/products/[id]" | "/profile"
type AppRouteHandlerRoutes = "/api/admin/categories" | "/api/admin/categories/[id]" | "/api/admin/orders" | "/api/admin/orders/[id]" | "/api/admin/products" | "/api/admin/products/[id]" | "/api/banners" | "/api/categories" | "/api/orders" | "/api/products" | "/api/products/[id]" | "/api/stations"
type PageRoutes = never
type LayoutRoutes = "/" | "/admin"
type RedirectRoutes = never
type RewriteRoutes = never
type Routes = AppRoutes | PageRoutes | LayoutRoutes | RedirectRoutes | RewriteRoutes | AppRouteHandlerRoutes


interface ParamMap {
  "/": {}
  "/admin": {}
  "/admin/categories": {}
  "/admin/login": {}
  "/admin/orders": {}
  "/admin/products": {}
  "/api/admin/categories": {}
  "/api/admin/categories/[id]": { "id": string; }
  "/api/admin/orders": {}
  "/api/admin/orders/[id]": { "id": string; }
  "/api/admin/products": {}
  "/api/admin/products/[id]": { "id": string; }
  "/api/banners": {}
  "/api/categories": {}
  "/api/orders": {}
  "/api/products": {}
  "/api/products/[id]": { "id": string; }
  "/api/stations": {}
  "/auth/login": {}
  "/auth/register": {}
  "/cart": {}
  "/categories": {}
  "/categories/[id]": { "id": string; }
  "/checkout": {}
  "/orders": {}
  "/orders/[id]": { "id": string; }
  "/products": {}
  "/products/[id]": { "id": string; }
  "/profile": {}
}


export type ParamsOf<Route extends Routes> = ParamMap[Route]

interface LayoutSlotMap {
  "/": never
  "/admin": never
}


export type { AppRoutes, PageRoutes, LayoutRoutes, RedirectRoutes, RewriteRoutes, ParamMap, AppRouteHandlerRoutes }

declare global {
  /**
   * Props for Next.js App Router page components
   * @example
   * ```tsx
   * export default function Page(props: PageProps<'/blog/[slug]'>) {
   *   const { slug } = await props.params
   *   return <div>Blog post: {slug}</div>
   * }
   * ```
   */
  interface PageProps<AppRoute extends AppRoutes> {
    params: Promise<ParamMap[AppRoute]>
    searchParams: Promise<Record<string, string | string[] | undefined>>
  }

  /**
   * Props for Next.js App Router layout components
   * @example
   * ```tsx
   * export default function Layout(props: LayoutProps<'/dashboard'>) {
   *   return <div>{props.children}</div>
   * }
   * ```
   */
  type LayoutProps<LayoutRoute extends LayoutRoutes> = {
    params: Promise<ParamMap[LayoutRoute]>
    children: React.ReactNode
  } & {
    [K in LayoutSlotMap[LayoutRoute]]: React.ReactNode
  }

  /**
   * Context for Next.js App Router route handlers
   * @example
   * ```tsx
   * export async function GET(request: NextRequest, context: RouteContext<'/api/users/[id]'>) {
   *   const { id } = await context.params
   *   return Response.json({ id })
   * }
   * ```
   */
  interface RouteContext<AppRouteHandlerRoute extends AppRouteHandlerRoutes> {
    params: Promise<ParamMap[AppRouteHandlerRoute]>
  }
}
