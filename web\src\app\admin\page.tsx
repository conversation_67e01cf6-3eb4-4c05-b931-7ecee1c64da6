"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Package, 
  ShoppingCart, 
  Users, 
  TrendingUp,
  DollarSign,
  Eye,
  AlertCircle
} from "lucide-react"
import { formatVND } from "@/lib/vietnamese"
import { withAdminAuth } from "@/contexts/admin-auth-context"

interface DashboardStats {
  totalProducts: number
  totalOrders: number
  totalRevenue: number
  pendingOrders: number
  activeUsers: number
  lowStockProducts: number
}

function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalOrders: 0,
    totalRevenue: 0,
    pendingOrders: 0,
    activeUsers: 0,
    lowStockProducts: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch dashboard statistics
        // For now, we'll use mock data since we need to implement the API endpoints
        setStats({
          totalProducts: 156,
          totalOrders: 1234,
          totalRevenue: 45678900,
          pendingOrders: 23,
          activeUsers: 567,
          lowStockProducts: 8
        })
      } catch (error) {
        console.error('Error fetching dashboard data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Tổng quan</h1>
          <p className="text-muted-foreground">
            Chào mừng bạn đến với bảng điều khiển quản trị SachMart
          </p>
        </div>
        
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  <div className="h-4 bg-muted rounded animate-pulse"></div>
                </CardTitle>
                <div className="h-4 w-4 bg-muted rounded animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-muted rounded animate-pulse mb-2"></div>
                <div className="h-3 bg-muted rounded animate-pulse w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  const statCards = [
    {
      title: "Tổng sản phẩm",
      value: stats.totalProducts.toLocaleString(),
      description: "Sản phẩm đang bán",
      icon: Package,
      color: "text-blue-600"
    },
    {
      title: "Tổng đơn hàng",
      value: stats.totalOrders.toLocaleString(),
      description: "Đơn hàng đã tạo",
      icon: ShoppingCart,
      color: "text-green-600"
    },
    {
      title: "Doanh thu",
      value: formatVND(stats.totalRevenue),
      description: "Tổng doanh thu",
      icon: DollarSign,
      color: "text-yellow-600"
    },
    {
      title: "Người dùng hoạt động",
      value: stats.activeUsers.toLocaleString(),
      description: "Người dùng trong tháng",
      icon: Users,
      color: "text-purple-600"
    }
  ]

  const alertCards = [
    {
      title: "Đơn hàng chờ xử lý",
      value: stats.pendingOrders,
      description: "Cần xem xét",
      icon: AlertCircle,
      color: "text-orange-600",
      urgent: stats.pendingOrders > 20
    },
    {
      title: "Sản phẩm sắp hết hàng",
      value: stats.lowStockProducts,
      description: "Cần nhập thêm",
      icon: Package,
      color: "text-red-600",
      urgent: stats.lowStockProducts > 5
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Tổng quan</h1>
        <p className="text-muted-foreground">
          Chào mừng bạn đến với bảng điều khiển quản trị SachMart
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statCards.map((card, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {card.title}
              </CardTitle>
              <card.icon className={`h-4 w-4 ${card.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <p className="text-xs text-muted-foreground">
                {card.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Alert Cards */}
      <div className="grid gap-4 md:grid-cols-2">
        {alertCards.map((card, index) => (
          <Card key={index} className={card.urgent ? "border-destructive" : ""}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                {card.title}
                {card.urgent && (
                  <Badge variant="destructive" className="text-xs">
                    Khẩn cấp
                  </Badge>
                )}
              </CardTitle>
              <card.icon className={`h-4 w-4 ${card.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <p className="text-xs text-muted-foreground">
                {card.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Hành động nhanh</CardTitle>
          <CardDescription>
            Các tác vụ thường dùng trong quản trị
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
              <CardContent className="p-4 text-center">
                <Package className="h-8 w-8 mx-auto mb-2 text-primary" />
                <h3 className="font-medium">Thêm sản phẩm mới</h3>
                <p className="text-sm text-muted-foreground">
                  Tạo sản phẩm mới
                </p>
              </CardContent>
            </Card>
            
            <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
              <CardContent className="p-4 text-center">
                <ShoppingCart className="h-8 w-8 mx-auto mb-2 text-primary" />
                <h3 className="font-medium">Xem đơn hàng</h3>
                <p className="text-sm text-muted-foreground">
                  Quản lý đơn hàng
                </p>
              </CardContent>
            </Card>
            
            <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
              <CardContent className="p-4 text-center">
                <TrendingUp className="h-8 w-8 mx-auto mb-2 text-primary" />
                <h3 className="font-medium">Xem báo cáo</h3>
                <p className="text-sm text-muted-foreground">
                  Thống kê chi tiết
                </p>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default withAdminAuth(AdminDashboard, "analytics.read")
