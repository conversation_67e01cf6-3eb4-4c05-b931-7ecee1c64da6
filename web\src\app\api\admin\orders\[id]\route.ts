import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

// Helper function to check admin permissions
async function checkAdminPermission(permission: string) {
  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return { authorized: false, error: 'Unauthorized' }
    }

    const { data, error } = await supabase
      .rpc('check_admin_permission', { permission_name: permission })

    if (error) {
      console.error('Permission check error:', error)
      // For development: if function doesn't exist, allow access
      if (error.message?.includes('function') || error.code === '42883') {
        console.log('Admin functions not available, allowing access for development')
        return { authorized: true, error: null }
      }
      return { authorized: false, error: 'Permission check failed' }
    }

    return { authorized: data === true, error: null }
  } catch (error) {
    console.error('Auth check error:', error)
    return { authorized: false, error: 'Auth check failed' }
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin permission
    const { authorized, error } = await checkAdminPermission('orders.read')
    if (!authorized) {
      return NextResponse.json(
        { error: error || 'Không có quyền truy cập' },
        { status: 401 }
      )
    }

    const { data: order, error: fetchError } = await supabase
      .from('orders')
      .select(`
        *,
        order_items (
          quantity,
          price,
          products (
            id,
            name,
            image,
            categories (
              id,
              name,
              image
            )
          )
        )
      `)
      .eq('id', params.id)
      .single()

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Không tìm thấy đơn hàng' },
          { status: 404 }
        )
      }
      console.error('Error fetching order:', fetchError)
      return NextResponse.json(
        { error: 'Không thể tải đơn hàng' },
        { status: 500 }
      )
    }

    // Transform response
    const transformedOrder = {
      id: order.id,
      userId: order.user_id,
      status: order.status,
      paymentStatus: order.payment_status,
      createdAt: order.created_at,
      receivedAt: order.received_at,
      delivery: order.delivery,
      total: order.total,
      note: order.note,
      items: order.order_items?.map((item: any) => ({
        quantity: item.quantity,
        price: item.price,
        product: {
          id: item.products.id,
          name: item.products.name,
          image: item.products.image,
          category: item.products.categories
        }
      })) || []
    }

    return NextResponse.json(transformedOrder)
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin permission
    const { authorized, error } = await checkAdminPermission('orders.write')
    if (!authorized) {
      return NextResponse.json(
        { error: error || 'Không có quyền cập nhật đơn hàng' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { status, paymentStatus, note } = body

    // Validation
    const validStatuses = ['pending', 'confirmed', 'shipping', 'completed', 'cancelled']
    const validPaymentStatuses = ['pending', 'success', 'failed']

    if (status && !validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Trạng thái đơn hàng không hợp lệ' },
        { status: 400 }
      )
    }

    if (paymentStatus && !validPaymentStatuses.includes(paymentStatus)) {
      return NextResponse.json(
        { error: 'Trạng thái thanh toán không hợp lệ' },
        { status: 400 }
      )
    }

    // Check if order exists
    const { data: existingOrder } = await supabase
      .from('orders')
      .select('id, status')
      .eq('id', params.id)
      .single()

    if (!existingOrder) {
      return NextResponse.json(
        { error: 'Không tìm thấy đơn hàng' },
        { status: 404 }
      )
    }

    // Prepare update data
    const updateData: any = {}
    if (status) updateData.status = status
    if (paymentStatus) updateData.payment_status = paymentStatus
    if (note !== undefined) updateData.note = note

    // Set received_at when status changes to completed
    if (status === 'completed' && existingOrder.status !== 'completed') {
      updateData.received_at = new Date().toISOString()
    }

    // Update order
    const { data: order, error: updateError } = await supabase
      .from('orders')
      .update(updateData)
      .eq('id', params.id)
      .select(`
        *,
        order_items (
          quantity,
          price,
          products (
            id,
            name,
            image,
            categories (
              id,
              name,
              image
            )
          )
        )
      `)
      .single()

    if (updateError) {
      console.error('Error updating order:', updateError)
      return NextResponse.json(
        { error: 'Không thể cập nhật đơn hàng' },
        { status: 500 }
      )
    }

    // Transform response
    const transformedOrder = {
      id: order.id,
      userId: order.user_id,
      status: order.status,
      paymentStatus: order.payment_status,
      createdAt: order.created_at,
      receivedAt: order.received_at,
      delivery: order.delivery,
      total: order.total,
      note: order.note,
      items: order.order_items?.map((item: any) => ({
        quantity: item.quantity,
        price: item.price,
        product: {
          id: item.products.id,
          name: item.products.name,
          image: item.products.image,
          category: item.products.categories
        }
      })) || []
    }

    return NextResponse.json(transformedOrder)
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}
