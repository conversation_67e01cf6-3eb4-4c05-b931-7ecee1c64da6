import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

// Helper function to check admin permissions
async function checkAdminPermission(permission: string) {
  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()

    if (sessionError || !session) {
      return { authorized: false, error: 'Unauthorized' }
    }

    const { data, error } = await supabase
      .rpc('check_admin_permission', { permission_name: permission })

    if (error) {
      console.error('Permission check error:', error)
      // For development: if function doesn't exist, allow access
      if (error.message?.includes('function') || error.code === '42883') {
        console.log('Admin functions not available, allowing access for development')
        return { authorized: true, error: null }
      }
      return { authorized: false, error: 'Permission check failed' }
    }

    return { authorized: data === true, error: null }
  } catch (error) {
    console.error('Auth check error:', error)
    return { authorized: false, error: 'Auth check failed' }
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check admin permission
    const { authorized, error } = await checkAdminPermission('categories.read')
    if (!authorized) {
      return NextResponse.json(
        { error: error || 'Không có quyền truy cập' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const limit = searchParams.get('limit')
    const offset = searchParams.get('offset')

    let query = supabase
      .from('categories')
      .select('*')

    // Search by name if provided
    if (search) {
      query = query.ilike('name', `%${search}%`)
    }

    // Add pagination
    if (limit) {
      query = query.limit(parseInt(limit))
    }
    if (offset) {
      query = query.range(parseInt(offset), parseInt(offset) + (parseInt(limit || '10')) - 1)
    }

    // Order by id
    query = query.order('id')

    const { data: categories, error: fetchError } = await query

    if (fetchError) {
      console.error('Error fetching categories:', fetchError)
      return NextResponse.json(
        { error: 'Không thể tải danh mục' },
        { status: 500 }
      )
    }

    return NextResponse.json(categories)
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check admin permission
    const { authorized, error } = await checkAdminPermission('categories.write')
    if (!authorized) {
      return NextResponse.json(
        { error: error || 'Không có quyền tạo danh mục' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { name, image } = body

    // Validation
    if (!name || !image) {
      return NextResponse.json(
        { error: 'Tên danh mục và hình ảnh là bắt buộc' },
        { status: 400 }
      )
    }

    if (name.length < 2 || name.length > 255) {
      return NextResponse.json(
        { error: 'Tên danh mục phải từ 2 đến 255 ký tự' },
        { status: 400 }
      )
    }

    // Check if category name already exists
    const { data: existingCategory } = await supabase
      .from('categories')
      .select('id')
      .eq('name', name)
      .single()

    if (existingCategory) {
      return NextResponse.json(
        { error: 'Tên danh mục đã tồn tại' },
        { status: 400 }
      )
    }

    // Create category
    const { data: category, error: createError } = await supabase
      .from('categories')
      .insert({
        name: name.trim(),
        image: image.trim()
      })
      .select()
      .single()

    if (createError) {
      console.error('Error creating category:', createError)
      return NextResponse.json(
        { error: 'Không thể tạo danh mục' },
        { status: 500 }
      )
    }

    return NextResponse.json(category, { status: 201 })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}
