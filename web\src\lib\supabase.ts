import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types based on our schema
export interface Category {
  id: number
  name: string
  image: string
  created_at?: string
}

export interface Product {
  id: number
  category_id: number
  name: string
  price: number
  original_price: number
  image: string
  detail: string
  created_at?: string
}

export interface Banner {
  id: number
  image_url: string
  title?: string
  description?: string
  link?: string
  created_at?: string
}

export interface Station {
  id: number
  name: string
  image: string
  address: string
  location: {
    lat: number
    lng: number
  }
  created_at?: string
}

export interface OrderItem {
  product: Product & {
    category: Category
  }
  quantity: number
}

export interface Order {
  id: number
  user_id: string
  status: 'pending' | 'confirmed' | 'shipping' | 'completed' | 'cancelled'
  payment_status: 'pending' | 'success' | 'failed'
  created_at: string
  received_at?: string
  items: OrderItem[]
  delivery: {
    type: 'shipping' | 'pickup'
    alias?: string
    address?: string
    name?: string
    phone?: string
    id?: number
    location?: {
      lat: number
      lng: number
    }
  }
  total: number
  note?: string
}
