-- QUICK <NAME_EMAIL> ADMIN ACCESS
-- Run this in Supabase SQL Editor to diagnose and fix the issue

-- ============================================================================
-- QUICK DIAGNOSIS
-- ============================================================================

-- 1. Check if admin tables exist
SELECT 
  CASE 
    WHEN COUNT(*) = 2 THEN 'TABLES OK: Admin schema exists'
    ELSE 'TABLES MISSING: Run admin-schema.sql first'
  END as table_status
FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name IN ('admin_roles', 'admin_users');

-- 2. Check if user exists in auth.users
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') 
    THEN 'USER OK: <EMAIL> exists in auth.users'
    ELSE 'USER MISSING: <EMAIL> not found in auth.users'
  END as user_status;

-- 3. Check if user is in admin_users (this is likely the issue)
SELECT 
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM admin_users au 
      JOIN auth.users u ON au.id = u.id 
      WHERE u.email = '<EMAIL>' AND au.is_active = true
    ) THEN 'ADMIN OK: User is active admin'
    WHEN EXISTS (
      SELECT 1 FROM admin_users au 
      JOIN auth.users u ON au.id = u.id 
      WHERE u.email = '<EMAIL>' AND au.is_active = false
    ) THEN 'ADMIN INACTIVE: User exists but inactive'
    ELSE 'ADMIN MISSING: User not in admin_users table - THIS IS THE ISSUE'
  END as admin_status;

-- ============================================================================
-- AUTOMATIC FIX
-- ============================================================================

-- Add <EMAIL> to admin_users with super_admin role
INSERT INTO admin_users (id, role_id, is_active, created_at) 
SELECT 
  u.id,
  (SELECT id FROM admin_roles WHERE name = 'super_admin'),
  true,
  NOW()
FROM auth.users u 
WHERE u.email = '<EMAIL>'
ON CONFLICT (id) DO UPDATE SET
  role_id = (SELECT id FROM admin_roles WHERE name = 'super_admin'),
  is_active = true,
  updated_at = NOW();

-- ============================================================================
-- VERIFICATION
-- ============================================================================

-- Verify the fix worked
SELECT 
  'VERIFICATION RESULT:' as result,
  u.email,
  ar.name as role,
  au.is_active,
  CASE 
    WHEN au.is_active = true THEN 'SUCCESS: <EMAIL> can now access admin interface'
    ELSE 'FAILED: Still cannot access admin interface'
  END as status
FROM admin_users au
JOIN auth.users u ON au.id = u.id
JOIN admin_roles ar ON au.role_id = ar.id
WHERE u.email = '<EMAIL>';

-- Show user ID for reference
SELECT 
  'User <NAME_EMAIL>:' as info,
  id as user_id,
  email,
  created_at
FROM auth.users 
WHERE email = '<EMAIL>';
