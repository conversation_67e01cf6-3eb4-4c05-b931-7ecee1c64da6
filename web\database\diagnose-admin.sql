-- SachMart Admin System Diagnostic Script
-- Run this in Supabase SQL Editor to diagnose admin setup issues

-- ============================================================================
-- DIAGNOSTIC CHECKS
-- ============================================================================

-- 1. Check if admin tables exist
SELECT 
  'Admin Tables Check' as check_type,
  CASE 
    WHEN COUNT(*) = 2 THEN '✅ PASS: Both admin tables exist'
    ELSE '❌ FAIL: Missing admin tables. Run admin-schema.sql'
  END as result
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('admin_roles', 'admin_users');

-- 2. Check if admin functions exist
SELECT 
  'Admin Functions Check' as check_type,
  CASE 
    WHEN COUNT(*) >= 2 THEN '✅ PASS: Admin functions exist'
    ELSE '❌ FAIL: Missing admin functions. Run admin-schema.sql'
  END as result
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('check_admin_permission', 'get_admin_user_info');

-- 3. Check if admin roles are populated
SELECT 
  'Admin Roles Check' as check_type,
  CASE 
    WHEN COUNT(*) >= 4 THEN '✅ PASS: Admin roles exist'
    ELSE '❌ FAIL: Missing admin roles. Run admin-schema.sql'
  END as result
FROM admin_roles;

-- 4. Check current user authentication
SELECT 
  'User Authentication Check' as check_type,
  CASE 
    WHEN auth.uid() IS NOT NULL THEN '✅ PASS: User is authenticated'
    ELSE '❌ FAIL: User not authenticated. Please login first'
  END as result;

-- 5. Check if current user is admin
SELECT 
  'Admin User Check' as check_type,
  CASE 
    WHEN EXISTS (SELECT 1 FROM admin_users WHERE id = auth.uid()) THEN '✅ PASS: User is admin'
    ELSE '❌ FAIL: User not in admin_users table'
  END as result;

-- ============================================================================
-- DETAILED INFORMATION
-- ============================================================================

-- Show current user info
SELECT 
  'Current User Info' as info_type,
  auth.uid() as user_id,
  (SELECT email FROM auth.users WHERE id = auth.uid()) as email;

-- Show all admin roles
SELECT 'Admin Roles' as info_type, name, description, permissions 
FROM admin_roles 
ORDER BY id;

-- Show all admin users
SELECT 
  'Admin Users' as info_type,
  au.id,
  u.email,
  ar.name as role_name,
  au.is_active,
  au.created_at
FROM admin_users au
JOIN auth.users u ON au.id = u.id
JOIN admin_roles ar ON au.role_id = ar.id
ORDER BY au.created_at;

-- ============================================================================
-- QUICK FIXES
-- ============================================================================

-- If you need to make the current user an admin, uncomment and run:
-- INSERT INTO admin_users (id, role_id, is_active, created_at) 
-- VALUES (
--   auth.uid(),
--   (SELECT id FROM admin_roles WHERE name = 'super_admin'),
--   true,
--   NOW()
-- )
-- ON CONFLICT (id) DO UPDATE SET
--   role_id = (SELECT id FROM admin_roles WHERE name = 'super_admin'),
--   is_active = true;

-- If you know the email of the user you want to make admin:
-- INSERT INTO admin_users (id, role_id, is_active, created_at) 
-- SELECT 
--   u.id,
--   (SELECT id FROM admin_roles WHERE name = 'super_admin'),
--   true,
--   NOW()
-- FROM auth.users u 
-- WHERE u.email = '<EMAIL>'  -- Replace with actual email
-- ON CONFLICT (id) DO UPDATE SET
--   role_id = (SELECT id FROM admin_roles WHERE name = 'super_admin'),
--   is_active = true;

-- ============================================================================
-- TEST FUNCTIONS
-- ============================================================================

-- Test permission check function (should work after setup)
-- SELECT check_admin_permission('categories.read') as permission_test;

-- Test admin user info function (should work after setup)
-- SELECT * FROM get_admin_user_info() as admin_info_test;
