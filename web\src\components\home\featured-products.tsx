import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ProductCard } from "@/components/product/product-card"
import { VIETNAMESE_LABELS } from "@/lib/vietnamese"

interface Product {
  id: number
  name: string
  price: number
  originalPrice: number
  image: string
  detail?: string
}

interface FeaturedProductsProps {
  products: Product[]
}

export function FeaturedProducts({ products }: FeaturedProductsProps) {
  if (!products || products.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Không có sản phẩm nổi bật để hiển thị</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold mb-2">Sản phẩm nổi bật</h2>
          <p className="text-muted-foreground">Những sản phẩm đư<PERSON><PERSON> yêu thích nhất</p>
        </div>
        <Button variant="outline" asChild>
          <Link href="/products">
            <PERSON>em tất cả
          </Link>
        </Button>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {products.slice(0, 10).map((product) => (
          <ProductCard
            key={product.id}
            product={product}
          />
        ))}
      </div>
    </div>
  )
}
