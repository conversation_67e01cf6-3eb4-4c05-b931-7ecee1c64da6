"use client"

import Image from "next/image"
import Link from "next/link"
import { MainLayout } from "@/components/layout/main-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Trash2, Minus, Plus, ShoppingBag } from "lucide-react"
import { formatVND, VIETNAMESE_LABELS } from "@/lib/vietnamese"
import { useCart } from "@/contexts/cart-context"

export default function CartPage() {
  const { state: cartState, removeItem, updateQuantity, clearCart } = useCart()

  const handleQuantityChange = (productId: number, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(productId)
    } else {
      updateQuantity(productId, newQuantity)
    }
  }

  const handleRemoveItem = (productId: number) => {
    removeItem(productId)
  }

  const handleClearCart = () => {
    if (confirm('<PERSON><PERSON><PERSON> c<PERSON> chắc chắn muốn xóa tất cả sản phẩm khỏi giỏ hàng?')) {
      clearCart()
    }
  }

  if (cartState.items.length === 0) {
    return (
      <MainLayout>
        <div className="py-8">
          <div className="text-center space-y-6">
            <ShoppingBag className="w-24 h-24 mx-auto text-muted-foreground" />
            <div>
              <h1 className="text-2xl font-bold mb-2">{VIETNAMESE_LABELS.cartEmpty}</h1>
              <p className="text-muted-foreground">
                Hãy thêm một số sản phẩm vào giỏ hàng của bạn
              </p>
            </div>
            <Button asChild>
              <Link href="/products">
                Tiếp tục mua sắm
              </Link>
            </Button>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">{VIETNAMESE_LABELS.cart}</h1>
          <Button 
            variant="outline" 
            onClick={handleClearCart}
            className="text-destructive hover:text-destructive"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Xóa tất cả
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-4">
            {cartState.items.map((item) => (
              <Card key={item.product.id}>
                <CardContent className="p-6">
                  <div className="flex gap-4">
                    {/* Product Image */}
                    <div className="relative w-20 h-20 flex-shrink-0">
                      <Image
                        src={item.product.image}
                        alt={item.product.name}
                        fill
                        className="object-cover rounded-md"
                      />
                    </div>

                    {/* Product Info */}
                    <div className="flex-1 space-y-2">
                      <Link 
                        href={`/products/${item.product.id}`}
                        className="font-medium hover:text-primary line-clamp-2"
                      >
                        {item.product.name}
                      </Link>
                      
                      <div className="flex items-center gap-2">
                        <span className="font-bold text-primary">
                          {formatVND(item.product.price)}
                        </span>
                        {item.product.originalPrice > item.product.price && (
                          <span className="text-sm text-muted-foreground line-through">
                            {formatVND(item.product.originalPrice)}
                          </span>
                        )}
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => handleQuantityChange(item.product.id, item.quantity - 1)}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <span className="w-8 text-center text-sm font-medium">
                            {item.quantity}
                          </span>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => handleQuantityChange(item.product.id, item.quantity + 1)}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>

                        <div className="flex items-center gap-2">
                          <span className="font-medium">
                            {formatVND(item.product.price * item.quantity)}
                          </span>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-destructive hover:text-destructive"
                            onClick={() => handleRemoveItem(item.product.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle>Tóm tắt đơn hàng</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Số lượng sản phẩm:</span>
                    <span>{cartState.itemCount}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>{VIETNAMESE_LABELS.subtotal}:</span>
                    <span>{formatVND(cartState.total)}</span>
                  </div>
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>Phí vận chuyển:</span>
                    <span>Miễn phí</span>
                  </div>
                </div>
                
                <Separator />
                
                <div className="flex justify-between font-bold text-lg">
                  <span>{VIETNAMESE_LABELS.total}:</span>
                  <span className="text-primary">{formatVND(cartState.total)}</span>
                </div>

                <div className="space-y-2">
                  <Button className="w-full" size="lg" asChild>
                    <Link href="/checkout">
                      {VIETNAMESE_LABELS.checkout}
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full" asChild>
                    <Link href="/products">
                      Tiếp tục mua sắm
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
