[{"id": 1, "status": "pending", "paymentStatus": "pending", "createdAt": "2023-10-01T10:00:00Z", "receivedAt": "2023-10-05T10:00:00Z", "items": [{"product": {"id": 1, "name": "<PERSON><PERSON>o giòn nhập khẩu - 1kg", "price": 67900, "originalPrice": 69900, "image": "https://zalo-miniapp.github.io/zaui-market/dummy/product/apples.png", "category": {"id": 1, "name": "Fruits", "image": "https://zalo-miniapp.github.io/zaui-market/dummy/category/fruits.png"}, "detail": "- Xuất xứ: <PERSON><PERSON>\n<PERSON> <PERSON><PERSON> cách đóng túi: 400 - 500g(+/-100g), 2-3 trái\n- <PERSON><PERSON><PERSON> ý: Trong quá trình vận chuyển có thể xảy ra tình trạng trái bị va chạm cấn trái nhẹ\n- Hướng dẫn bảo quản: <PERSON><PERSON><PERSON> quản ngăn mát tủ lạnh để đảm bảo độ tươi ngon."}, "quantity": 3}, {"product": {"id": 2, "name": "<PERSON><PERSON><PERSON> ng<PERSON>t thủy canh 300g", "price": 27900, "originalPrice": 30000, "image": "https://zalo-miniapp.github.io/zaui-market/dummy/product/cabbage.png", "category": {"id": 1, "name": "Vegetables", "image": "https://zalo-miniapp.github.io/zaui-market/dummy/category/vegetables.png"}, "detail": "<PERSON><PERSON><PERSON> ng<PERSON>t thủ<PERSON> canh, t<PERSON><PERSON><PERSON> ngon và giàu dinh dưỡng."}, "quantity": 2}, {"product": {"id": 3, "name": "<PERSON><PERSON>", "price": 313000, "originalPrice": 500000, "image": "https://zalo-miniapp.github.io/zaui-market/dummy/product/creamy.png", "category": {"id": 1, "name": "Desserts", "image": "https://zalo-miniapp.github.io/zaui-market/dummy/category/desserts.png"}, "detail": "<PERSON><PERSON>, thơm ngon và mát lạnh."}, "quantity": 1}], "delivery": {"type": "shipping", "alias": "Home", "address": "123 Main St, City, Country", "name": "<PERSON>", "phone": "123456789"}, "total": 203700, "note": "Lựa gi<PERSON>p mình táo ngon nhé, mình cảm ơn shop"}, {"id": 2, "status": "completed", "paymentStatus": "success", "createdAt": "2023-09-20T08:30:00Z", "receivedAt": "2023-09-25T08:30:00Z", "items": [{"product": {"id": 2, "name": "<PERSON><PERSON><PERSON> ng<PERSON>t thủy canh 300g", "price": 27900, "originalPrice": 30000, "image": "https://zalo-miniapp.github.io/zaui-market/dummy/product/cabbage.png", "category": {"id": 1, "name": "Vegetables", "image": "https://zalo-miniapp.github.io/zaui-market/dummy/category/vegetables.png"}, "detail": "<PERSON><PERSON><PERSON> ng<PERSON>t thủ<PERSON> canh, t<PERSON><PERSON><PERSON> ngon và giàu dinh dưỡng."}, "quantity": 1}, {"product": {"id": 3, "name": "<PERSON><PERSON>", "price": 313000, "originalPrice": 500000, "image": "https://zalo-miniapp.github.io/zaui-market/dummy/product/creamy.png", "category": {"id": 1, "name": "Desserts", "image": "https://zalo-miniapp.github.io/zaui-market/dummy/category/desserts.png"}, "detail": "<PERSON><PERSON>, thơm ngon và mát lạnh."}, "quantity": 3}, {"product": {"id": 4, "name": "<PERSON><PERSON><PERSON> quy hộp thi<PERSON>c", "price": 150000, "originalPrice": 300000, "image": "https://zalo-miniapp.github.io/zaui-market/dummy/product/danisa.png", "category": {"id": 1, "name": "Snacks", "image": "https://zalo-miniapp.github.io/zaui-market/dummy/category/snacks.png"}, "detail": "<PERSON><PERSON><PERSON> quy hộ<PERSON> th<PERSON>, gi<PERSON><PERSON> tan và thơm ngon."}, "quantity": 2}], "delivery": {"type": "pickup", "id": 1, "name": "Station 1", "image": "https://zalo-miniapp.github.io/zaui-market/dummy/station/station1.png", "address": "456 Another St, City, Country", "location": {"lat": 10.762622, "lng": 106.660172}}, "total": 982900, "note": "Lựa gi<PERSON>p mình táo ngon nhé, mình cảm ơn shop"}, {"id": 3, "status": "shipping", "paymentStatus": "success", "createdAt": "2023-10-10T12:00:00Z", "receivedAt": "2023-10-15T12:00:00Z", "items": [{"product": {"id": 4, "name": "<PERSON><PERSON><PERSON> quy hộp thi<PERSON>c", "price": 150000, "originalPrice": 300000, "image": "https://zalo-miniapp.github.io/zaui-market/dummy/product/danisa.png", "category": {"id": 1, "name": "Snacks", "image": "https://zalo-miniapp.github.io/zaui-market/dummy/category/snacks.png"}, "detail": "<PERSON><PERSON><PERSON> quy hộ<PERSON> th<PERSON>, gi<PERSON><PERSON> tan và thơm ngon."}, "quantity": 3}, {"product": {"id": 5, "name": "Bội nồi 4 món", "price": 150000, "originalPrice": 300000, "image": "https://zalo-miniapp.github.io/zaui-market/dummy/product/pans.png", "category": {"id": 1, "name": "Kitchenware", "image": "https://zalo-miniapp.github.io/zaui-market/dummy/category/kitchenware.png"}, "detail": "<PERSON><PERSON><PERSON> nồi 4 món, tiện lợi và bền bỉ."}, "quantity": 1}, {"product": {"id": 6, "name": "Bột giặt có nước xả", "price": 150000, "originalPrice": 300000, "image": "https://zalo-miniapp.github.io/zaui-market/dummy/product/tide.png", "category": {"id": 1, "name": "Household", "image": "https://zalo-miniapp.github.io/zaui-market/dummy/category/household.png"}, "detail": "B<PERSON>t giặt có nước xả, sạch sẽ và thơm mát."}, "quantity": 2}], "delivery": {"type": "shipping", "alias": "Office", "address": "789 Office St, City, Country", "name": "<PERSON>", "phone": "987654321"}, "total": 450000, "note": "Lựa gi<PERSON>p mình táo ngon nhé, mình cảm ơn shop"}, {"id": 4, "status": "completed", "paymentStatus": "failed", "createdAt": "2023-09-15T09:00:00Z", "receivedAt": "2023-09-20T09:00:00Z", "items": [{"product": {"id": 5, "name": "Bội nồi 4 món", "price": 150000, "originalPrice": 300000, "image": "https://zalo-miniapp.github.io/zaui-market/dummy/product/pans.png", "category": {"id": 1, "name": "Kitchenware", "image": "https://zalo-miniapp.github.io/zaui-market/dummy/category/kitchenware.png"}, "detail": "<PERSON><PERSON><PERSON> nồi 4 món, tiện lợi và bền bỉ."}, "quantity": 1}, {"product": {"id": 6, "name": "Bột giặt có nước xả", "price": 150000, "originalPrice": 300000, "image": "https://zalo-miniapp.github.io/zaui-market/dummy/product/tide.png", "category": {"id": 1, "name": "Household", "image": "https://zalo-miniapp.github.io/zaui-market/dummy/category/household.png"}, "detail": "B<PERSON>t giặt có nước xả, sạch sẽ và thơm mát."}, "quantity": 3}, {"product": {"id": 7, "name": "<PERSON><PERSON><PERSON> c<PERSON>m điện xanh d<PERSON>", "price": 150000, "originalPrice": 300000, "image": "https://zalo-miniapp.github.io/zaui-market/dummy/product/sunhouse.png", "category": {"id": 1, "name": "Electronics", "image": "https://zalo-miniapp.github.io/zaui-market/dummy/category/electronics.png"}, "detail": "<PERSON><PERSON><PERSON> cơm điện xanh <PERSON>, tiện lợi và bền bỉ."}, "quantity": 1}], "delivery": {"type": "pickup", "id": 2, "name": "Tiệm Tí Hon - Chi nhánh 2", "image": "https://zalo-miniapp.github.io/zaui-market/dummy/product/apples.png", "address": "<PERSON>06, <PERSON><PERSON> 13, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 7, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, Vi<PERSON>t Nam", "location": {"lat": 10.768746, "lng": 106.728228}}, "total": 600000, "note": "Lựa gi<PERSON>p mình táo ngon nhé, mình cảm ơn shop"}]