-- Admin roles and permissions extension for <PERSON>ch<PERSON>art
-- This extends the existing schema with admin functionality

-- Create admin roles table
CREATE TABLE IF NOT EXISTS admin_roles (
  id SERIAL PRIMARY KEY,
  name VARCHAR(50) UNIQUE NOT NULL,
  description TEXT,
  permissions JSONB NOT NULL DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create admin users table (extends auth.users)
CREATE TABLE IF NOT EXISTS admin_users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  role_id INTEGER REFERENCES admin_roles(id) ON DELETE SET NULL,
  is_active BOOLEAN DEFAULT true,
  last_login TIMESTAMP WITH TIME ZONE,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default admin roles
INSERT INTO admin_roles (name, description, permissions) VALUES
('super_admin', 'Super Administrator with full access', 
 '["users.read", "users.write", "users.delete", "products.read", "products.write", "products.delete", "categories.read", "categories.write", "categories.delete", "orders.read", "orders.write", "orders.delete", "banners.read", "banners.write", "banners.delete", "stations.read", "stations.write", "stations.delete", "analytics.read", "settings.read", "settings.write"]'),
('admin', 'Administrator with most permissions', 
 '["products.read", "products.write", "categories.read", "categories.write", "orders.read", "orders.write", "banners.read", "banners.write", "stations.read", "stations.write", "analytics.read"]'),
('manager', 'Manager with limited permissions', 
 '["products.read", "products.write", "categories.read", "orders.read", "orders.write", "analytics.read"]'),
('viewer', 'Read-only access', 
 '["products.read", "categories.read", "orders.read", "analytics.read"]')
ON CONFLICT (name) DO NOTHING;

-- Enable RLS for admin tables
ALTER TABLE admin_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- Create policies for admin_roles (only super_admin can manage roles)
CREATE POLICY "Super admins can view all roles" ON admin_roles FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM admin_users au 
    JOIN admin_roles ar ON au.role_id = ar.id 
    WHERE au.id = auth.uid() AND ar.name = 'super_admin' AND au.is_active = true
  )
);

CREATE POLICY "Super admins can manage roles" ON admin_roles FOR ALL 
USING (
  EXISTS (
    SELECT 1 FROM admin_users au 
    JOIN admin_roles ar ON au.role_id = ar.id 
    WHERE au.id = auth.uid() AND ar.name = 'super_admin' AND au.is_active = true
  )
);

-- Create policies for admin_users
CREATE POLICY "Admins can view admin users" ON admin_users FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM admin_users au 
    JOIN admin_roles ar ON au.role_id = ar.id 
    WHERE au.id = auth.uid() AND au.is_active = true
  )
);

CREATE POLICY "Super admins can manage admin users" ON admin_users FOR ALL 
USING (
  EXISTS (
    SELECT 1 FROM admin_users au 
    JOIN admin_roles ar ON au.role_id = ar.id 
    WHERE au.id = auth.uid() AND ar.name = 'super_admin' AND au.is_active = true
  )
);

-- Update existing table policies to allow admin access

-- Categories - Allow admins to manage
CREATE POLICY "Admins can manage categories" ON categories FOR ALL 
USING (
  EXISTS (
    SELECT 1 FROM admin_users au 
    JOIN admin_roles ar ON au.role_id = ar.id 
    WHERE au.id = auth.uid() AND au.is_active = true
    AND (ar.permissions ? 'categories.write' OR ar.permissions ? 'categories.delete')
  )
);

-- Products - Allow admins to manage
CREATE POLICY "Admins can manage products" ON products FOR ALL 
USING (
  EXISTS (
    SELECT 1 FROM admin_users au 
    JOIN admin_roles ar ON au.role_id = ar.id 
    WHERE au.id = auth.uid() AND au.is_active = true
    AND (ar.permissions ? 'products.write' OR ar.permissions ? 'products.delete')
  )
);

-- Banners - Allow admins to manage
CREATE POLICY "Admins can manage banners" ON banners FOR ALL 
USING (
  EXISTS (
    SELECT 1 FROM admin_users au 
    JOIN admin_roles ar ON au.role_id = ar.id 
    WHERE au.id = auth.uid() AND au.is_active = true
    AND (ar.permissions ? 'banners.write' OR ar.permissions ? 'banners.delete')
  )
);

-- Stations - Allow admins to manage
CREATE POLICY "Admins can manage stations" ON stations FOR ALL 
USING (
  EXISTS (
    SELECT 1 FROM admin_users au 
    JOIN admin_roles ar ON au.role_id = ar.id 
    WHERE au.id = auth.uid() AND au.is_active = true
    AND (ar.permissions ? 'stations.write' OR ar.permissions ? 'stations.delete')
  )
);

-- Orders - Allow admins to view and update all orders
CREATE POLICY "Admins can view all orders" ON orders FOR SELECT 
USING (
  auth.uid() = user_id OR 
  EXISTS (
    SELECT 1 FROM admin_users au 
    JOIN admin_roles ar ON au.role_id = ar.id 
    WHERE au.id = auth.uid() AND au.is_active = true
    AND ar.permissions ? 'orders.read'
  )
);

CREATE POLICY "Admins can update orders" ON orders FOR UPDATE 
USING (
  auth.uid() = user_id OR 
  EXISTS (
    SELECT 1 FROM admin_users au 
    JOIN admin_roles ar ON au.role_id = ar.id 
    WHERE au.id = auth.uid() AND au.is_active = true
    AND ar.permissions ? 'orders.write'
  )
);

-- Order items - Allow admins to view all order items
CREATE POLICY "Admins can view all order items" ON order_items FOR SELECT 
USING (
  EXISTS (SELECT 1 FROM orders WHERE orders.id = order_items.order_id AND orders.user_id = auth.uid()) OR
  EXISTS (
    SELECT 1 FROM admin_users au 
    JOIN admin_roles ar ON au.role_id = ar.id 
    WHERE au.id = auth.uid() AND au.is_active = true
    AND ar.permissions ? 'orders.read'
  )
);

-- Create function to check admin permissions
CREATE OR REPLACE FUNCTION check_admin_permission(permission_name TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM admin_users au 
    JOIN admin_roles ar ON au.role_id = ar.id 
    WHERE au.id = auth.uid() 
    AND au.is_active = true
    AND ar.permissions ? permission_name
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get admin user info
CREATE OR REPLACE FUNCTION get_admin_user_info()
RETURNS TABLE (
  user_id UUID,
  email TEXT,
  role_name TEXT,
  permissions JSONB,
  is_active BOOLEAN,
  last_login TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    au.id,
    u.email,
    ar.name,
    ar.permissions,
    au.is_active,
    au.last_login
  FROM admin_users au
  JOIN admin_roles ar ON au.role_id = ar.id
  JOIN auth.users u ON au.id = u.id
  WHERE au.id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_admin_users_role_id ON admin_users(role_id);
CREATE INDEX IF NOT EXISTS idx_admin_users_is_active ON admin_users(is_active);
CREATE INDEX IF NOT EXISTS idx_admin_roles_permissions ON admin_roles USING GIN(permissions);

-- Update function to update last_login
CREATE OR REPLACE FUNCTION update_admin_last_login()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE admin_users 
  SET last_login = NOW() 
  WHERE id = NEW.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update last_login on auth.users update
DROP TRIGGER IF EXISTS trigger_update_admin_last_login ON auth.users;
CREATE TRIGGER trigger_update_admin_last_login
  AFTER UPDATE OF last_sign_in_at ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION update_admin_last_login();

-- Sample admin user setup (for development/testing)
-- Note: In production, create admin users through proper signup process
-- This is just for initial setup and testing

-- Insert a sample admin user (you'll need to create this user in Supabase Auth first)
-- Example: <EMAIL> with password: admin123456
-- Then run this to make them an admin:

-- INSERT INTO admin_users (id, role_id, is_active, created_at)
-- SELECT
--   auth.uid(),
--   (SELECT id FROM admin_roles WHERE name = 'super_admin'),
--   true,
--   NOW()
-- WHERE auth.uid() IS NOT NULL
-- ON CONFLICT (id) DO UPDATE SET
--   role_id = (SELECT id FROM admin_roles WHERE name = 'super_admin'),
--   is_active = true;

-- To create an admin user:
-- 1. Sign up normally through Supabase Auth (or create in Supabase dashboard)
-- 2. Get the user ID from auth.users table
-- 3. Insert into admin_users table with appropriate role_id
