import Link from "next/link"
import { Separator } from "@/components/ui/separator"

export function Footer() {
  return (
    <footer className="border-t bg-background">
      <div className="px-4 sm:px-6 lg:px-8 xl:px-12 2xl:px-16 py-8 md:py-12">
        <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-4">
          {/* Company Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">SachMart</h3>
            <p className="text-sm text-muted-foreground">
              <PERSON><PERSON><PERSON> thị trực tuyến hàng đầu Việt Nam, cung cấp thực phẩm tươi ngon và an toàn cho mọi gia đình.
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold"><PERSON><PERSON><PERSON> k<PERSON><PERSON> nhanh</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/" className="text-muted-foreground hover:text-primary">
                  Trang chủ
                </Link>
              </li>
              <li>
                <Link href="/categories" className="text-muted-foreground hover:text-primary">
                  Danh mục sản phẩm
                </Link>
              </li>
              <li>
                <Link href="/products" className="text-muted-foreground hover:text-primary">
                  Sản phẩm
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-muted-foreground hover:text-primary">
                  Về chúng tôi
                </Link>
              </li>
            </ul>
          </div>

          {/* Customer Service */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold">Hỗ trợ khách hàng</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/contact" className="text-muted-foreground hover:text-primary">
                  Liên hệ
                </Link>
              </li>
              <li>
                <Link href="/faq" className="text-muted-foreground hover:text-primary">
                  Câu hỏi thường gặp
                </Link>
              </li>
              <li>
                <Link href="/shipping" className="text-muted-foreground hover:text-primary">
                  Chính sách giao hàng
                </Link>
              </li>
              <li>
                <Link href="/returns" className="text-muted-foreground hover:text-primary">
                  Chính sách đổi trả
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold">Thông tin liên hệ</h4>
            <div className="space-y-2 text-sm text-muted-foreground">
              <p>📍 123 Nguyễn Văn Linh, Quận 7, TP.HCM</p>
              <p>📞 Hotline: 1900 1234</p>
              <p>✉️ Email: <EMAIL></p>
              <p>🕒 Giờ làm việc: 8:00 - 22:00 (T2-CN)</p>
            </div>
          </div>
        </div>

        <Separator className="my-8" />

        <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
          <p className="text-sm text-muted-foreground">
            © 2024 SachMart. Tất cả quyền được bảo lưu.
          </p>
          <div className="flex space-x-4 text-sm">
            <Link href="/privacy" className="text-muted-foreground hover:text-primary">
              Chính sách bảo mật
            </Link>
            <Link href="/terms" className="text-muted-foreground hover:text-primary">
              Điều khoản sử dụng
            </Link>
          </div>
        </div>
        </div>
      </div>
    </footer>
  )
}
