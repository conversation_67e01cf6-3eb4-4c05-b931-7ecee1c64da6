-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Categories table
CREATE TABLE categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  image TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table
CREATE TABLE products (
  id SERIAL PRIMARY KEY,
  category_id INTEGER REFERENCES categories(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  price INTEGER NOT NULL, -- Price in VND (stored as integer to avoid floating point issues)
  original_price INTEGER NOT NULL,
  image TEXT NOT NULL,
  detail TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Banners table
CREATE TABLE banners (
  id SERIAL PRIMARY KEY,
  image_url TEXT NOT NULL,
  title VARCHAR(255),
  description TEXT,
  link TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Stations table
CREATE TABLE stations (
  id SERIAL PRIMARY KEY,
  name VA<PERSON>HAR(255) NOT NULL,
  image TEXT NOT NULL,
  address TEXT NOT NULL,
  location JSONB NOT NULL, -- Store lat/lng as JSON
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Orders table
CREATE TABLE orders (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'shipping', 'completed', 'cancelled')),
  payment_status VARCHAR(50) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'success', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  received_at TIMESTAMP WITH TIME ZONE,
  delivery JSONB NOT NULL, -- Store delivery info as JSON
  total INTEGER NOT NULL,
  note TEXT
);

-- Order items table
CREATE TABLE order_items (
  id SERIAL PRIMARY KEY,
  order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
  product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
  quantity INTEGER NOT NULL DEFAULT 1,
  price INTEGER NOT NULL, -- Price at time of order
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert sample categories (Vietnamese)
INSERT INTO categories (name, image) VALUES
('Rau, củ, quả', 'https://zalo-miniapp.github.io/zaui-market/dummy/category/fruits.png'),
('Thịt', 'https://zalo-miniapp.github.io/zaui-market/dummy/category/meat.png'),
('Thủy hải sản', 'https://zalo-miniapp.github.io/zaui-market/dummy/category/fish.png'),
('Bơ, sữa, trứng', 'https://zalo-miniapp.github.io/zaui-market/dummy/category/egg.png'),
('Bánh tươi', 'https://zalo-miniapp.github.io/zaui-market/dummy/category/bread.png'),
('Bánh kẹo ngũ cốc', 'https://zalo-miniapp.github.io/zaui-market/dummy/category/cookies.png'),
('Thực phẩm chế biến', 'https://zalo-miniapp.github.io/zaui-market/dummy/category/sushi.png'),
('Thực phẩm đông lạnh', 'https://zalo-miniapp.github.io/zaui-market/dummy/category/ice-cream.png'),
('Gia vị', 'https://zalo-miniapp.github.io/zaui-market/dummy/category/sauce.png'),
('Nước giải khát', 'https://zalo-miniapp.github.io/zaui-market/dummy/category/beverage.png');

-- Insert sample banners
INSERT INTO banners (image_url, title, description) VALUES
('https://zalo-miniapp.github.io/zaui-market/dummy/banner/ads.jpg', 'Khuyến mãi đặc biệt', 'Giảm giá lên đến 50% cho tất cả sản phẩm'),
('https://zalo-miniapp.github.io/zaui-market/dummy/banner/ads.jpg', 'Sản phẩm mới', 'Khám phá những sản phẩm tươi ngon nhất'),
('https://zalo-miniapp.github.io/zaui-market/dummy/banner/ads.jpg', 'Giao hàng miễn phí', 'Miễn phí giao hàng cho đơn hàng trên 500.000 VND'),
('https://zalo-miniapp.github.io/zaui-market/dummy/banner/ads.jpg', 'Chất lượng cao', 'Cam kết chất lượng tốt nhất cho khách hàng'),
('https://zalo-miniapp.github.io/zaui-market/dummy/banner/ads.jpg', 'Hỗ trợ 24/7', 'Đội ngũ hỗ trợ khách hàng luôn sẵn sàng');

-- Insert sample stations
INSERT INTO stations (name, image, address, location) VALUES
('Tiệm Tí Hon - Chi nhánh 1', 'https://zalo-miniapp.github.io/zaui-market/dummy/product/apples.png', 'Z06, Số 13, Tân Thuận Đông, Quận 7, Thành phố Hồ Chí Minh, Việt Nam', '{"lat": 10.768746, "lng": 106.728228}'),
('Tiệm Tí Hon - Chi nhánh 2', 'https://zalo-miniapp.github.io/zaui-market/dummy/product/apples.png', 'Z06, Số 13, Tân Thuận Đông, Quận 7, Thành phố Hồ Chí Minh, Việt Nam', '{"lat": 10.768746, "lng": 106.728228}'),
('Tiệm Tí Hon - Chi nhánh 3', 'https://zalo-miniapp.github.io/zaui-market/dummy/product/apples.png', '123 Nguyễn Văn Linh, Quận 1, Thành phố Hồ Chí Minh, Việt Nam', '{"lat": 10.762622, "lng": 106.660172}'),
('Tiệm Tí Hon - Chi nhánh 4', 'https://zalo-miniapp.github.io/zaui-market/dummy/product/apples.png', '456 Lê Văn Việt, Quận 9, Thành phố Hồ Chí Minh, Việt Nam', '{"lat": 10.850000, "lng": 106.800000}');

-- Create indexes for better performance
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_product_id ON order_items(product_id);

-- Enable Row Level Security (RLS)
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE banners ENABLE ROW LEVEL SECURITY;
ALTER TABLE stations ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access
CREATE POLICY "Categories are viewable by everyone" ON categories FOR SELECT USING (true);
CREATE POLICY "Products are viewable by everyone" ON products FOR SELECT USING (true);
CREATE POLICY "Banners are viewable by everyone" ON banners FOR SELECT USING (true);
CREATE POLICY "Stations are viewable by everyone" ON stations FOR SELECT USING (true);

-- Create policies for orders (users can only see their own orders)
CREATE POLICY "Users can view their own orders" ON orders FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own orders" ON orders FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own orders" ON orders FOR UPDATE USING (auth.uid() = user_id);

-- Create policies for order items (users can only see items from their own orders)
CREATE POLICY "Users can view their own order items" ON order_items FOR SELECT USING (
  EXISTS (SELECT 1 FROM orders WHERE orders.id = order_items.order_id AND orders.user_id = auth.uid())
);
CREATE POLICY "Users can insert their own order items" ON order_items FOR INSERT WITH CHECK (
  EXISTS (SELECT 1 FROM orders WHERE orders.id = order_items.order_id AND orders.user_id = auth.uid())
);
