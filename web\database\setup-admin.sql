-- Setup script for SachMart Admin System
-- Run this after setting up the main schema and admin-schema

-- This script helps create the first admin user
-- Replace 'your-user-id-here' with the actual UUID from auth.users

-- Step 1: First create a user through Supabase Auth (signup or dashboard)
-- Step 2: Find the user ID in auth.users table
-- Step 3: Replace the UUID below and run this script

-- Example: Make a user with email '<EMAIL>' a super admin
-- INSERT INTO admin_users (id, role_id, is_active, created_at) 
-- VALUES (
--   'your-user-id-here', -- Replace with actual UUID from auth.users
--   (SELECT id FROM admin_roles WHERE name = 'super_admin'),
--   true,
--   NOW()
-- );

-- Alternative: If you know the email, you can use this query
-- INSERT INTO admin_users (id, role_id, is_active, created_at) 
-- SELECT 
--   u.id,
--   (SELECT id FROM admin_roles WHERE name = 'super_admin'),
--   true,
--   NOW()
-- FROM auth.users u 
-- WHERE u.email = '<EMAIL>'
-- ON CONFLICT (id) DO UPDATE SET
--   role_id = (SELECT id FROM admin_roles WHERE name = 'super_admin'),
--   is_active = true;

-- Verify admin roles exist
SELECT * FROM admin_roles ORDER BY id;

-- Check if admin user was created successfully
-- SELECT 
--   au.id,
--   u.email,
--   ar.name as role_name,
--   au.is_active,
--   au.created_at
-- FROM admin_users au
-- JOIN auth.users u ON au.id = u.id
-- JOIN admin_roles ar ON au.role_id = ar.id;

-- Test permission function
-- SELECT check_admin_permission('categories.read');

-- Test admin user info function
-- SELECT * FROM get_admin_user_info();
