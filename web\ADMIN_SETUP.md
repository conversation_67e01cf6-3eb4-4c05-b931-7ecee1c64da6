# SachMart Admin Interface Setup Guide

This guide will help you set up and configure the administrative interface for SachMart.

## 🚀 Quick Start

### 1. Database Setup

First, ensure you have the main database schema set up, then run the admin schema:

```sql
-- Run in Supabase SQL Editor
-- 1. First run the main schema.sql if not already done
-- 2. Then run the admin schema
\i database/admin-schema.sql
```

### 2. Create Your First Admin User

#### Option A: Through Supabase Dashboard
1. Go to Supabase Dashboard → Authentication → Users
2. Create a new user with email `<EMAIL>` and password `admin123456`
3. Copy the User ID from the users table

#### Option B: Through Signup
1. Use the regular signup process to create a user
2. Find the user ID in the `auth.users` table

#### Option C: Using SQL
```sql
-- Replace 'your-user-id-here' with the actual UUID from auth.users
INSERT INTO admin_users (id, role_id, is_active, created_at) 
VALUES (
  'your-user-id-here',
  (SELECT id FROM admin_roles WHERE name = 'super_admin'),
  true,
  NOW()
);
```

### 3. Access the Admin Interface

1. Start the development server:
   ```bash
   cd web
   npm run dev
   ```

2. Navigate to: `http://localhost:3000/admin/login`

3. Login with your admin credentials

## 🏗️ Architecture Overview

### Authentication & Authorization

The admin system extends the existing Supabase authentication with role-based access control:

- **Admin Roles**: `super_admin`, `admin`, `manager`, `viewer`
- **Permissions**: Granular permissions for each resource (read, write, delete)
- **Row Level Security**: Database-level security policies

### Admin Roles & Permissions

| Role | Description | Permissions |
|------|-------------|-------------|
| `super_admin` | Full system access | All permissions |
| `admin` | Most administrative tasks | Products, Categories, Orders, Banners, Stations, Analytics |
| `manager` | Limited management access | Products, Categories, Orders (read/write), Analytics (read) |
| `viewer` | Read-only access | Products, Categories, Orders, Analytics (read only) |

### Available Permissions

- `users.read`, `users.write`, `users.delete`
- `products.read`, `products.write`, `products.delete`
- `categories.read`, `categories.write`, `categories.delete`
- `orders.read`, `orders.write`, `orders.delete`
- `banners.read`, `banners.write`, `banners.delete`
- `stations.read`, `stations.write`, `stations.delete`
- `analytics.read`
- `settings.read`, `settings.write`

## 📱 Admin Interface Features

### 1. Dashboard
- Overview statistics
- Quick actions
- System alerts

### 2. Categories Management ✅
- CRUD operations for product categories
- Image management
- Search and filtering

### 3. Products Management (Coming Soon)
- Product catalog management
- Category relationships
- Inventory tracking

### 4. Orders Management (Coming Soon)
- Order status updates
- Customer information
- Order tracking

### 5. Banners Management (Coming Soon)
- Homepage banner carousel
- Image upload and management
- Activation controls

### 6. Stations Management (Coming Soon)
- Pickup location management
- Address and location mapping
- Station status controls

## 🔧 Development Mode

For development purposes, the system includes fallback mechanisms:

- If admin database functions are not available, mock permissions are used
- Mock admin user is created automatically for testing
- All admin features are accessible in development mode

## 🛡️ Security Features

- **Row Level Security**: Database-level access control
- **Permission-based UI**: Interface elements shown based on user permissions
- **API Authentication**: All admin endpoints require valid authentication
- **Input Validation**: Client and server-side validation
- **Audit Trail**: Track admin actions (coming soon)

## 🚀 Deployment

### Production Setup

1. **Database**: Run admin schema in production database
2. **Environment**: Ensure all environment variables are set
3. **Admin Users**: Create admin users through secure process
4. **SSL**: Ensure HTTPS is enabled for admin interface

### Environment Variables

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## 🔍 Troubleshooting

### Common Issues

1. **Cannot access admin interface**
   - Check if user exists in `admin_users` table
   - Verify user has appropriate role and is active
   - Check Supabase connection

2. **Permission denied errors**
   - Verify user permissions in database
   - Check RLS policies are correctly applied
   - Ensure admin functions are created

3. **Database function errors**
   - Run the admin schema SQL file
   - Check if functions exist in database
   - Verify function permissions

### Debug Commands

```sql
-- Check admin roles
SELECT * FROM admin_roles ORDER BY id;

-- Check admin users
SELECT 
  au.id,
  u.email,
  ar.name as role_name,
  au.is_active,
  au.created_at
FROM admin_users au
JOIN auth.users u ON au.id = u.id
JOIN admin_roles ar ON au.role_id = ar.id;

-- Test permission function
SELECT check_admin_permission('categories.read');

-- Test admin user info function
SELECT * FROM get_admin_user_info();
```

## 📚 API Documentation

### Admin Endpoints

- `GET /api/admin/categories` - List categories
- `POST /api/admin/categories` - Create category
- `GET /api/admin/categories/[id]` - Get category
- `PUT /api/admin/categories/[id]` - Update category
- `DELETE /api/admin/categories/[id]` - Delete category

More endpoints will be added as features are implemented.

## 🤝 Contributing

When adding new admin features:

1. Add appropriate permissions to the roles
2. Create API endpoints with permission checks
3. Implement UI with permission-based access
4. Add proper validation and error handling
5. Update this documentation

## 📞 Support

For issues or questions:
- Check the troubleshooting section
- Review the database logs
- Verify Supabase configuration
- Check browser console for errors
