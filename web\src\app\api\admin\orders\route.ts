import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

// Helper function to check admin permissions
async function checkAdminPermission(permission: string) {
  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return { authorized: false, error: 'Unauthorized' }
    }

    const { data, error } = await supabase
      .rpc('check_admin_permission', { permission_name: permission })

    if (error) {
      console.error('Permission check error:', error)
      // For development: if function doesn't exist, allow access
      if (error.message?.includes('function') || error.code === '42883') {
        console.log('Admin functions not available, allowing access for development')
        return { authorized: true, error: null }
      }
      return { authorized: false, error: 'Permission check failed' }
    }

    return { authorized: data === true, error: null }
  } catch (error) {
    console.error('Auth check error:', error)
    return { authorized: false, error: 'Auth check failed' }
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check admin permission
    const { authorized, error } = await checkAdminPermission('orders.read')
    if (!authorized) {
      return NextResponse.json(
        { error: error || 'Không có quyền truy cập' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const paymentStatus = searchParams.get('paymentStatus')
    const limit = searchParams.get('limit')
    const offset = searchParams.get('offset')

    let query = supabase
      .from('orders')
      .select(`
        *,
        order_items (
          quantity,
          price,
          products (
            id,
            name,
            image,
            categories (
              id,
              name,
              image
            )
          )
        )
      `)

    // Filter by status if provided
    if (status) {
      query = query.eq('status', status)
    }

    // Filter by payment status if provided
    if (paymentStatus) {
      query = query.eq('payment_status', paymentStatus)
    }

    // Add pagination
    if (limit) {
      query = query.limit(parseInt(limit))
    }
    if (offset) {
      query = query.range(parseInt(offset), parseInt(offset) + (parseInt(limit || '10')) - 1)
    }

    // Order by created_at desc
    query = query.order('created_at', { ascending: false })

    const { data: orders, error: fetchError } = await query

    if (fetchError) {
      console.error('Error fetching orders:', fetchError)
      return NextResponse.json(
        { error: 'Không thể tải đơn hàng' },
        { status: 500 }
      )
    }

    // Transform data to match expected format
    const transformedOrders = orders?.map(order => ({
      id: order.id,
      userId: order.user_id,
      status: order.status,
      paymentStatus: order.payment_status,
      createdAt: order.created_at,
      receivedAt: order.received_at,
      delivery: order.delivery,
      total: order.total,
      note: order.note,
      items: order.order_items?.map((item: any) => ({
        quantity: item.quantity,
        price: item.price,
        product: {
          id: item.products.id,
          name: item.products.name,
          image: item.products.image,
          category: item.products.categories
        }
      })) || []
    })) || []

    return NextResponse.json(transformedOrders)
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}
