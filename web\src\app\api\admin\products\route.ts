import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

// Helper function to check admin permissions
async function checkAdminPermission(permission: string) {
  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session) {
      return { authorized: false, error: 'Unauthorized' }
    }

    const { data, error } = await supabase
      .rpc('check_admin_permission', { permission_name: permission })

    if (error) {
      console.error('Permission check error:', error)
      // For development: if function doesn't exist, allow access
      if (error.message?.includes('function') || error.code === '42883') {
        console.log('Admin functions not available, allowing access for development')
        return { authorized: true, error: null }
      }
      return { authorized: false, error: 'Permission check failed' }
    }

    return { authorized: data === true, error: null }
  } catch (error) {
    console.error('Auth check error:', error)
    return { authorized: false, error: 'Auth check failed' }
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check admin permission
    const { authorized, error } = await checkAdminPermission('products.read')
    if (!authorized) {
      return NextResponse.json(
        { error: error || 'Không có quyền truy cập' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const categoryId = searchParams.get('categoryId')
    const limit = searchParams.get('limit')
    const offset = searchParams.get('offset')

    let query = supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          image
        )
      `)

    // Search by name if provided
    if (search) {
      query = query.ilike('name', `%${search}%`)
    }

    // Filter by category if provided
    if (categoryId) {
      query = query.eq('category_id', categoryId)
    }

    // Add pagination
    if (limit) {
      query = query.limit(parseInt(limit))
    }
    if (offset) {
      query = query.range(parseInt(offset), parseInt(offset) + (parseInt(limit || '10')) - 1)
    }

    // Order by id
    query = query.order('id')

    const { data: products, error: fetchError } = await query

    if (fetchError) {
      console.error('Error fetching products:', fetchError)
      return NextResponse.json(
        { error: 'Không thể tải sản phẩm' },
        { status: 500 }
      )
    }

    // Transform data to match expected format
    const transformedProducts = products?.map(product => ({
      id: product.id,
      categoryId: product.category_id,
      name: product.name,
      price: product.price,
      originalPrice: product.original_price,
      image: product.image,
      detail: product.detail,
      category: product.categories,
      created_at: product.created_at
    })) || []

    return NextResponse.json(transformedProducts)
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check admin permission
    const { authorized, error } = await checkAdminPermission('products.write')
    if (!authorized) {
      return NextResponse.json(
        { error: error || 'Không có quyền tạo sản phẩm' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { name, categoryId, price, originalPrice, image, detail } = body

    // Validation
    if (!name || !categoryId || !price || !originalPrice || !image) {
      return NextResponse.json(
        { error: 'Tên, danh mục, giá, giá gốc và hình ảnh là bắt buộc' },
        { status: 400 }
      )
    }

    if (name.length < 2 || name.length > 255) {
      return NextResponse.json(
        { error: 'Tên sản phẩm phải từ 2 đến 255 ký tự' },
        { status: 400 }
      )
    }

    if (price <= 0 || originalPrice <= 0) {
      return NextResponse.json(
        { error: 'Giá phải lớn hơn 0' },
        { status: 400 }
      )
    }

    if (price > originalPrice) {
      return NextResponse.json(
        { error: 'Giá bán không thể lớn hơn giá gốc' },
        { status: 400 }
      )
    }

    // Check if category exists
    const { data: category } = await supabase
      .from('categories')
      .select('id')
      .eq('id', categoryId)
      .single()

    if (!category) {
      return NextResponse.json(
        { error: 'Danh mục không tồn tại' },
        { status: 400 }
      )
    }

    // Create product
    const { data: product, error: createError } = await supabase
      .from('products')
      .insert({
        name: name.trim(),
        category_id: categoryId,
        price: parseInt(price),
        original_price: parseInt(originalPrice),
        image: image.trim(),
        detail: detail?.trim() || ''
      })
      .select(`
        *,
        categories (
          id,
          name,
          image
        )
      `)
      .single()

    if (createError) {
      console.error('Error creating product:', createError)
      return NextResponse.json(
        { error: 'Không thể tạo sản phẩm' },
        { status: 500 }
      )
    }

    // Transform response
    const transformedProduct = {
      id: product.id,
      categoryId: product.category_id,
      name: product.name,
      price: product.price,
      originalPrice: product.original_price,
      image: product.image,
      detail: product.detail,
      category: product.categories,
      created_at: product.created_at
    }

    return NextResponse.json(transformedProduct, { status: 201 })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}
