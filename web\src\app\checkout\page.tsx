"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { MainLayout } from "@/components/layout/main-layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { MapPin, CreditCard, Truck, Store, Loader2 } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { useCart } from "@/contexts/cart-context"
import { formatVND, VIETNAMESE_LABELS } from "@/lib/vietnamese"

interface Station {
  id: number
  name: string
  address: string
  location: {
    lat: number
    lng: number
  }
}

export default function CheckoutPage() {
  const { user, loading: authLoading } = useAuth()
  const { state: cartState, clearCart } = useCart()
  const router = useRouter()
  
  const [stations, setStations] = useState<Station[]>([])
  const [loading, setLoading] = useState(false)
  const [deliveryType, setDeliveryType] = useState<'shipping' | 'pickup'>('shipping')
  const [selectedStation, setSelectedStation] = useState<number | null>(null)
  const [paymentMethod, setPaymentMethod] = useState<'cod' | 'card'>('cod')
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    phone: '',
    address: '',
    note: ''
  })
  const [error, setError] = useState('')

  useEffect(() => {
    if (!authLoading && !user) {
      router.push("/auth/login")
    }
  }, [user, authLoading, router])

  useEffect(() => {
    if (cartState.items.length === 0) {
      router.push("/cart")
    }
  }, [cartState.items.length, router])

  useEffect(() => {
    fetchStations()
  }, [])

  const fetchStations = async () => {
    try {
      const response = await fetch('/api/stations')
      if (response.ok) {
        const data = await response.json()
        setStations(data)
        if (data.length > 0) {
          setSelectedStation(data[0].id)
        }
      }
    } catch (error) {
      console.error('Error fetching stations:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    // Validation
    if (deliveryType === 'shipping') {
      if (!customerInfo.name || !customerInfo.phone || !customerInfo.address) {
        setError('Vui lòng nhập đầy đủ thông tin giao hàng')
        setLoading(false)
        return
      }
    } else {
      if (!selectedStation) {
        setError('Vui lòng chọn cửa hàng để lấy hàng')
        setLoading(false)
        return
      }
    }

    try {
      const orderData = {
        items: cartState.items,
        delivery: {
          type: deliveryType,
          ...(deliveryType === 'shipping' ? {
            name: customerInfo.name,
            phone: customerInfo.phone,
            address: customerInfo.address
          } : {
            stationId: selectedStation
          })
        },
        paymentMethod,
        note: customerInfo.note,
        total: cartState.total
      }

      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      })

      if (response.ok) {
        const order = await response.json()
        clearCart()
        router.push(`/orders/${order.id}`)
      } else {
        setError('Có lỗi xảy ra khi đặt hàng. Vui lòng thử lại.')
      }
    } catch (error) {
      setError('Có lỗi xảy ra khi đặt hàng. Vui lòng thử lại.')
    } finally {
      setLoading(false)
    }
  }

  if (authLoading) {
    return (
      <MainLayout>
        <div className="py-8">
          <div className="text-center">
            <p className="text-muted-foreground">{VIETNAMESE_LABELS.loading}</p>
          </div>
        </div>
      </MainLayout>
    )
  }

  if (!user || cartState.items.length === 0) {
    return null
  }

  return (
    <MainLayout>
      <div className="py-8">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold mb-8">{VIETNAMESE_LABELS.checkout}</h1>
          
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Checkout Form */}
              <div className="lg:col-span-2 space-y-6">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {/* Delivery Method */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Truck className="h-5 w-5" />
                      Phương thức nhận hàng
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <RadioGroup 
                      value={deliveryType} 
                      onValueChange={(value: 'shipping' | 'pickup') => setDeliveryType(value)}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="shipping" id="shipping" />
                        <Label htmlFor="shipping" className="flex items-center gap-2">
                          <Truck className="h-4 w-4" />
                          Giao hàng tận nơi
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="pickup" id="pickup" />
                        <Label htmlFor="pickup" className="flex items-center gap-2">
                          <Store className="h-4 w-4" />
                          Lấy tại cửa hàng
                        </Label>
                      </div>
                    </RadioGroup>
                  </CardContent>
                </Card>

                {/* Delivery Information */}
                {deliveryType === 'shipping' ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <MapPin className="h-5 w-5" />
                        Thông tin giao hàng
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="name">Họ và tên *</Label>
                          <Input
                            id="name"
                            value={customerInfo.name}
                            onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
                            placeholder="Nhập họ và tên"
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="phone">Số điện thoại *</Label>
                          <Input
                            id="phone"
                            value={customerInfo.phone}
                            onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
                            placeholder="Nhập số điện thoại"
                            required
                          />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="address">Địa chỉ giao hàng *</Label>
                        <Textarea
                          id="address"
                          value={customerInfo.address}
                          onChange={(e) => setCustomerInfo(prev => ({ ...prev, address: e.target.value }))}
                          placeholder="Nhập địa chỉ chi tiết"
                          required
                        />
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Store className="h-5 w-5" />
                        Chọn cửa hàng
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <RadioGroup 
                        value={selectedStation?.toString()} 
                        onValueChange={(value) => setSelectedStation(parseInt(value))}
                      >
                        {stations.map((station) => (
                          <div key={station.id} className="flex items-center space-x-2">
                            <RadioGroupItem value={station.id.toString()} id={`station-${station.id}`} />
                            <Label htmlFor={`station-${station.id}`} className="flex-1">
                              <div>
                                <p className="font-medium">{station.name}</p>
                                <p className="text-sm text-muted-foreground">{station.address}</p>
                              </div>
                            </Label>
                          </div>
                        ))}
                      </RadioGroup>
                    </CardContent>
                  </Card>
                )}

                {/* Payment Method */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CreditCard className="h-5 w-5" />
                      Phương thức thanh toán
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <RadioGroup 
                      value={paymentMethod} 
                      onValueChange={(value: 'cod' | 'card') => setPaymentMethod(value)}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="cod" id="cod" />
                        <Label htmlFor="cod">Thanh toán khi nhận hàng (COD)</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="card" id="card" />
                        <Label htmlFor="card">Thanh toán online</Label>
                      </div>
                    </RadioGroup>
                  </CardContent>
                </Card>

                {/* Order Note */}
                <Card>
                  <CardHeader>
                    <CardTitle>Ghi chú đơn hàng</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Textarea
                      value={customerInfo.note}
                      onChange={(e) => setCustomerInfo(prev => ({ ...prev, note: e.target.value }))}
                      placeholder="Ghi chú thêm cho đơn hàng (tùy chọn)"
                    />
                  </CardContent>
                </Card>
              </div>

              {/* Order Summary */}
              <div className="lg:col-span-1">
                <Card className="sticky top-4">
                  <CardHeader>
                    <CardTitle>Tóm tắt đơn hàng</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Order Items */}
                    <div className="space-y-3">
                      {cartState.items.map((item) => (
                        <div key={item.product.id} className="flex items-center gap-3">
                          <div className="relative w-12 h-12 flex-shrink-0">
                            <Image
                              src={item.product.image}
                              alt={item.product.name}
                              fill
                              className="object-cover rounded"
                            />
                          </div>
                          <div className="flex-1">
                            <p className="font-medium text-sm line-clamp-2">
                              {item.product.name}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {formatVND(item.product.price)} x {item.quantity}
                            </p>
                          </div>
                          <p className="text-sm font-medium">
                            {formatVND(item.product.price * item.quantity)}
                          </p>
                        </div>
                      ))}
                    </div>

                    <Separator />

                    {/* Totals */}
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{VIETNAMESE_LABELS.subtotal}:</span>
                        <span>{formatVND(cartState.total)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Phí vận chuyển:</span>
                        <span>Miễn phí</span>
                      </div>
                    </div>

                    <Separator />

                    <div className="flex justify-between font-bold text-lg">
                      <span>{VIETNAMESE_LABELS.total}:</span>
                      <span className="text-primary">{formatVND(cartState.total)}</span>
                    </div>

                    <Button type="submit" className="w-full" size="lg" disabled={loading}>
                      {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Đặt hàng
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </form>
        </div>
      </div>
    </MainLayout>
  )
}
