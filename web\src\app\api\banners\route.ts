import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import fs from 'fs'
import path from 'path'

// Mock data fallback
function getMockBanners() {
  try {
    const mockBannersPath = path.join(process.cwd(), '..', 'api', 'mock-output', 'banners.json')
    const bannersData = fs.readFileSync(mockBannersPath, 'utf8')
    return JSON.parse(bannersData)
  } catch (error) {
    console.error('Error loading mock banners:', error)
    return []
  }
}

export async function GET() {
  try {
    // Try Supabase first
    try {
      const { data: banners, error } = await supabase
        .from('banners')
        .select('*')
        .eq('is_active', true)
        .order('id')

      if (!error && banners) {
        // Transform to match the expected format (array of image URLs)
        const bannerUrls = banners.map(banner => banner.image_url)
        return NextResponse.json(bannerUrls)
      }
    } catch (supabaseError) {
      console.log('Supabase not available, using mock data')
    }

    // Fallback to mock data
    const banners = getMockBanners()
    return NextResponse.json(banners)
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}
