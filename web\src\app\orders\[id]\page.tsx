"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { MainLayout } from "@/components/layout/main-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, Package, Truck, CheckCircle, XCircle, MapPin, CreditCard } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { formatVND, formatDate, VIETNAMESE_LABELS } from "@/lib/vietnamese"

interface OrderItem {
  product: {
    id: number
    name: string
    price: number
    originalPrice: number
    image: string
    category: {
      id: number
      name: string
      image: string
    }
    detail: string
  }
  quantity: number
}

interface Order {
  id: number
  status: 'pending' | 'confirmed' | 'shipping' | 'completed' | 'cancelled'
  paymentStatus: 'pending' | 'success' | 'failed'
  createdAt: string
  receivedAt?: string
  items: OrderItem[]
  delivery: {
    type: 'shipping' | 'pickup'
    alias?: string
    address?: string
    name?: string
    phone?: string
    id?: number
    location?: {
      lat: number
      lng: number
    }
  }
  total: number
  note?: string
}

export default function OrderDetailPage() {
  const params = useParams()
  const orderId = params.id as string
  const { user, loading: authLoading } = useAuth()
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    if (!authLoading && !user) {
      router.push("/auth/login")
    }
  }, [user, authLoading, router])

  useEffect(() => {
    if (user && orderId) {
      fetchOrder()
    }
  }, [user, orderId])

  const fetchOrder = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}`)
      if (response.ok) {
        const data = await response.json()
        setOrder(data)
      } else if (response.status === 401) {
        router.push("/auth/login")
      } else if (response.status === 404) {
        router.push("/orders")
      }
    } catch (error) {
      console.error('Error fetching order:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Package className="h-4 w-4" />
      case 'confirmed':
        return <Package className="h-4 w-4" />
      case 'shipping':
        return <Truck className="h-4 w-4" />
      case 'completed':
        return <CheckCircle className="h-4 w-4" />
      case 'cancelled':
        return <XCircle className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'pending':
        return 'secondary'
      case 'confirmed':
        return 'default'
      case 'shipping':
        return 'default'
      case 'completed':
        return 'default'
      case 'cancelled':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  const getPaymentStatusVariant = (status: string) => {
    switch (status) {
      case 'pending':
        return 'secondary'
      case 'success':
        return 'default'
      case 'failed':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  if (authLoading || loading) {
    return (
      <MainLayout>
        <div className="py-8">
          <div className="text-center">
            <p className="text-muted-foreground">{VIETNAMESE_LABELS.loading}</p>
          </div>
        </div>
      </MainLayout>
    )
  }

  if (!user) {
    return null
  }

  if (!order) {
    return (
      <MainLayout>
        <div className="py-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-2xl font-bold mb-4">Không tìm thấy đơn hàng</h1>
            <Button asChild>
              <Link href="/orders">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Quay lại danh sách đơn hàng
              </Link>
            </Button>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-6">
            <Button variant="ghost" asChild className="mb-4">
              <Link href="/orders">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Quay lại đơn hàng
              </Link>
            </Button>
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold">Đơn hàng #{order.id}</h1>
                <p className="text-muted-foreground">
                  Đặt hàng: {formatDate(order.createdAt)}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={getStatusVariant(order.status)}>
                  {getStatusIcon(order.status)}
                  <span className="ml-1">
                    {VIETNAMESE_LABELS.orderStatus[order.status]}
                  </span>
                </Badge>
                <Badge variant={getPaymentStatusVariant(order.paymentStatus)}>
                  {VIETNAMESE_LABELS.paymentStatus[order.paymentStatus]}
                </Badge>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Order Details */}
            <div className="lg:col-span-2 space-y-6">
              {/* Order Items */}
              <Card>
                <CardHeader>
                  <CardTitle>Sản phẩm đã đặt</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {order.items.map((item, index) => (
                    <div key={index} className="flex items-center gap-4">
                      <div className="relative w-16 h-16 flex-shrink-0">
                        <Image
                          src={item.product.image}
                          alt={item.product.name}
                          fill
                          className="object-cover rounded"
                        />
                      </div>
                      <div className="flex-1">
                        <Link 
                          href={`/products/${item.product.id}`}
                          className="font-medium hover:text-primary line-clamp-2"
                        >
                          {item.product.name}
                        </Link>
                        <p className="text-sm text-muted-foreground">
                          {item.product.category.name}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="font-medium text-primary">
                            {formatVND(item.product.price)}
                          </span>
                          {item.product.originalPrice > item.product.price && (
                            <span className="text-sm text-muted-foreground line-through">
                              {formatVND(item.product.originalPrice)}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">x{item.quantity}</p>
                        <p className="text-sm text-muted-foreground">
                          {formatVND(item.product.price * item.quantity)}
                        </p>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Delivery Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Thông tin giao hàng
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {order.delivery.type === 'shipping' ? (
                    <div>
                      <p className="font-medium mb-2">Giao hàng tận nơi</p>
                      <div className="text-sm text-muted-foreground space-y-1">
                        <p><strong>Người nhận:</strong> {order.delivery.name}</p>
                        <p><strong>Số điện thoại:</strong> {order.delivery.phone}</p>
                        <p><strong>Địa chỉ:</strong> {order.delivery.address}</p>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <p className="font-medium mb-2">Lấy tại cửa hàng</p>
                      <div className="text-sm text-muted-foreground">
                        <p>{order.delivery.address}</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Payment Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    Thông tin thanh toán
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-sm">
                    <p className="font-medium mb-1">Phương thức thanh toán:</p>
                    <p className="text-muted-foreground">
                      Thanh toán khi nhận hàng (COD)
                    </p>
                  </div>
                </CardContent>
              </Card>

              {order.note && (
                <Card>
                  <CardHeader>
                    <CardTitle>Ghi chú đơn hàng</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">{order.note}</p>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <Card className="sticky top-4">
                <CardHeader>
                  <CardTitle>Tóm tắt đơn hàng</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Số lượng sản phẩm:</span>
                      <span>{order.items.reduce((sum, item) => sum + item.quantity, 0)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>{VIETNAMESE_LABELS.subtotal}:</span>
                      <span>{formatVND(order.total)}</span>
                    </div>
                    <div className="flex justify-between text-sm text-muted-foreground">
                      <span>Phí vận chuyển:</span>
                      <span>Miễn phí</span>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="flex justify-between font-bold text-lg">
                    <span>{VIETNAMESE_LABELS.total}:</span>
                    <span className="text-primary">{formatVND(order.total)}</span>
                  </div>

                  {order.status === 'pending' && (
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full">
                        Hủy đơn hàng
                      </Button>
                    </div>
                  )}

                  <Button variant="outline" className="w-full" asChild>
                    <Link href="/products">
                      Tiếp tục mua sắm
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
