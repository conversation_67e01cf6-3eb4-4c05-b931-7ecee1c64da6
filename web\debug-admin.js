// Debug script to test admin login
// Run this in browser console on the admin login page

console.log('🔍 Admin Login Debug Script');

// Test 1: Check if we're on the right URL
console.log('Current URL:', window.location.href);
console.log('Expected URL should be: http://localhost:3001/admin/login');

// Test 2: Check if admin auth context is loaded
try {
  const adminAuthContext = document.querySelector('[data-admin-auth]');
  console.log('Admin auth context found:', !!adminAuthContext);
} catch (e) {
  console.log('Admin auth context check failed:', e);
}

// Test 3: Check if Supabase is loaded
try {
  console.log('Supabase client available:', typeof window.supabase !== 'undefined');
} catch (e) {
  console.log('Supabase check failed:', e);
}

// Test 4: Check for JavaScript errors
console.log('Check console for any red error messages above this line');

// Test 5: Check network requests
console.log('Open Network tab in DevTools and try to login');
console.log('Look for failed requests to /api/ endpoints');

// Test 6: Manual login test
console.log('To test login manually, run this in console:');
console.log(`
fetch('/api/auth/signin', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'your-password-here'
  })
}).then(r => r.json()).then(console.log);
`);

console.log('🔍 Debug script complete. Check the output above.');
