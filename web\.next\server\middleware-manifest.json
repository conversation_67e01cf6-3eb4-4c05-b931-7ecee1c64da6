{"version": 3, "middleware": {}, "sortedMiddleware": [], "functions": {"/icon/route": {"files": ["server/middleware-build-manifest.js", "server/interception-route-rewrite-manifest.js", "server/server-reference-manifest.js", "server/app/icon/route_client-reference-manifest.js", "server/edge/chunks/_next-internal_server_app_icon_route_actions_6c46f2f4.js", "server/edge/chunks/turbopack-_next-internal_server_app_icon_route_actions_7a96774c.js", "server/edge/chunks/node_modules_next_dist_esm_1f6c464f._.js", "server/edge/chunks/node_modules_next_dist_compiled_react-server-dom-turbopack_1d71ac1e._.js", "server/edge/chunks/node_modules_next_dist_compiled_@vercel_og_16be1ace._.js", "server/edge/chunks/node_modules_next_dist_compiled_8f4fd302._.js", "server/edge/chunks/node_modules_next_dist_7bea040f._.js", "server/edge/chunks/edge-wrapper_98c2e09e.js", "server/edge/chunks/[root-of-the-server]__bdb9cc89._.js", "server/edge/chunks/turbopack-edge-wrapper_ea2f910e.js"], "name": "app/icon/route", "page": "/icon/route", "matchers": [{"regexp": "^/icon(?:/)?$", "originalSource": "/icon"}], "wasm": [{"name": "wasm_fdb56e4cc5936d29", "filePath": "server/edge/chunks/node_modules_next_dist_compiled_@vercel_og_resvg_23ace1ce.wasm"}, {"name": "wasm_1ccdc983b1fd9f87", "filePath": "server/edge/chunks/node_modules_next_dist_compiled_@vercel_og_yoga_23ace1ce.wasm"}], "assets": [{"name": "server/edge/assets/noto-sans-v27-latin-regular.bdfa676c.ttf", "filePath": "server/edge/assets/noto-sans-v27-latin-regular.bdfa676c.ttf"}], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "c2WolBBUCp1bU98WkAXZq3MjcLMNTBcJ1AL3PTJRHcI=", "__NEXT_PREVIEW_MODE_ID": "5fcdf670e9d1f526e60515d43998c66e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4446183f2a28c2a75139ac2270ab0f1457fe1ca3624a7fcad5d74b7a88c5a079", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "57773fa719207f0dd7c60bdd368a1341cda34d77d3a2cde5a5e2772d61d9b917"}}}}