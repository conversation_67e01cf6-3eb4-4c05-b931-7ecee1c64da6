"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { MainLayout } from "@/components/layout/main-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { ShoppingBag, Package, Truck, CheckCircle, XCircle } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { formatVND, formatDate, VIETNAMESE_LABELS } from "@/lib/vietnamese"
import Link from "next/link"

interface OrderItem {
  product: {
    id: number
    name: string
    price: number
    originalPrice: number
    image: string
    category: {
      id: number
      name: string
      image: string
    }
    detail: string
  }
  quantity: number
}

interface Order {
  id: number
  status: 'pending' | 'confirmed' | 'shipping' | 'completed' | 'cancelled'
  paymentStatus: 'pending' | 'success' | 'failed'
  createdAt: string
  receivedAt?: string
  items: OrderItem[]
  delivery: {
    type: 'shipping' | 'pickup'
    alias?: string
    address?: string
    name?: string
    phone?: string
    id?: number
    location?: {
      lat: number
      lng: number
    }
  }
  total: number
  note?: string
}

export default function OrdersPage() {
  const { user, loading } = useAuth()
  const [orders, setOrders] = useState<Order[]>([])
  const [ordersLoading, setOrdersLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push("/auth/login")
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user) {
      fetchOrders()
    }
  }, [user])

  const fetchOrders = async () => {
    try {
      const response = await fetch('/api/orders')
      if (response.ok) {
        const data = await response.json()
        setOrders(data)
      } else if (response.status === 401) {
        router.push("/auth/login")
      }
    } catch (error) {
      console.error('Error fetching orders:', error)
    } finally {
      setOrdersLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Package className="h-4 w-4" />
      case 'confirmed':
        return <Package className="h-4 w-4" />
      case 'shipping':
        return <Truck className="h-4 w-4" />
      case 'completed':
        return <CheckCircle className="h-4 w-4" />
      case 'cancelled':
        return <XCircle className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'pending':
        return 'secondary'
      case 'confirmed':
        return 'default'
      case 'shipping':
        return 'default'
      case 'completed':
        return 'default'
      case 'cancelled':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  const getPaymentStatusVariant = (status: string) => {
    switch (status) {
      case 'pending':
        return 'secondary'
      case 'success':
        return 'default'
      case 'failed':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  if (loading || ordersLoading) {
    return (
      <MainLayout>
        <div className="py-8">
          <div className="text-center">
            <p className="text-muted-foreground">{VIETNAMESE_LABELS.loading}</p>
          </div>
        </div>
      </MainLayout>
    )
  }

  if (!user) {
    return null
  }

  return (
    <MainLayout>
      <div className="py-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{VIETNAMESE_LABELS.orders}</h1>
            <p className="text-muted-foreground">
              Theo dõi và quản lý đơn hàng của bạn
            </p>
          </div>

          {orders.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <ShoppingBag className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">Chưa có đơn hàng nào</h3>
                <p className="text-muted-foreground mb-4">
                  Bạn chưa đặt đơn hàng nào. Hãy bắt đầu mua sắm ngay!
                </p>
                <Button asChild>
                  <Link href="/products">Bắt đầu mua sắm</Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {orders.map((order) => (
                <Card key={order.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">
                        Đơn hàng #{order.id}
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge variant={getStatusVariant(order.status)}>
                          {getStatusIcon(order.status)}
                          <span className="ml-1">
                            {VIETNAMESE_LABELS.orderStatus[order.status]}
                          </span>
                        </Badge>
                        <Badge variant={getPaymentStatusVariant(order.paymentStatus)}>
                          {VIETNAMESE_LABELS.paymentStatus[order.paymentStatus]}
                        </Badge>
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Đặt hàng: {formatDate(order.createdAt)}
                      {order.receivedAt && (
                        <span> • Nhận hàng: {formatDate(order.receivedAt)}</span>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Order Items */}
                    <div className="space-y-3">
                      {order.items.map((item, index) => (
                        <div key={index} className="flex items-center gap-3">
                          <div className="relative w-12 h-12 flex-shrink-0">
                            <Image
                              src={item.product.image}
                              alt={item.product.name}
                              fill
                              className="object-cover rounded"
                            />
                          </div>
                          <div className="flex-1">
                            <p className="font-medium text-sm line-clamp-1">
                              {item.product.name}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {item.product.category.name}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium">
                              {formatVND(item.product.price)} x {item.quantity}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {formatVND(item.product.price * item.quantity)}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>

                    <Separator />

                    {/* Delivery Info */}
                    <div className="text-sm">
                      <p className="font-medium mb-1">Thông tin giao hàng:</p>
                      <p className="text-muted-foreground">
                        {order.delivery.type === 'shipping' ? (
                          <>
                            Giao hàng tận nơi<br />
                            {order.delivery.name} - {order.delivery.phone}<br />
                            {order.delivery.address}
                          </>
                        ) : (
                          <>
                            Lấy tại cửa hàng<br />
                            {order.delivery.address}
                          </>
                        )}
                      </p>
                    </div>

                    {order.note && (
                      <div className="text-sm">
                        <p className="font-medium mb-1">Ghi chú:</p>
                        <p className="text-muted-foreground">{order.note}</p>
                      </div>
                    )}

                    <Separator />

                    {/* Total */}
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Tổng cộng:</span>
                      <span className="text-lg font-bold text-primary">
                        {formatVND(order.total)}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  )
}
