"use client"

import React, { create<PERSON>ontext, useContext, useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'

// Admin user interface
interface AdminUser {
  id: string
  email: string
  role_name: string
  permissions: string[]
  is_active: boolean
  last_login?: string
}

interface AdminAuthContextType {
  user: AdminUser | null
  session: any | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error: any }>
  signOut: () => Promise<void>
  hasPermission: (permission: string) => boolean
  isAdmin: () => boolean
  refreshUser: () => Promise<void>
}

const AdminAuthContext = createContext<AdminAuthContextType | null>(null)

export function AdminAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AdminUser | null>(null)
  const [session, setSession] = useState<any | null>(null)
  const [loading, setLoading] = useState(true)

  // Check if user has specific permission
  const hasPermission = (permission: string): boolean => {
    if (!user || !user.is_active) return false
    return user.permissions.includes(permission)
  }

  // Check if user is admin
  const isAdmin = (): boolean => {
    return user !== null && user.is_active
  }

  // Fetch admin user info from database
  const fetchAdminUser = async (userId: string): Promise<AdminUser | null> => {
    try {
      const { data, error } = await supabase
        .rpc('get_admin_user_info')

      if (error) {
        console.error('Error fetching admin user:', error)
        // For development: if function doesn't exist, create a mock admin user
        if (error.message?.includes('function') || error.code === '42883') {
          console.log('Admin functions not available, using mock admin user for development')
          return {
            id: userId,
            email: '<EMAIL>',
            role_name: 'super_admin',
            permissions: [
              'users.read', 'users.write', 'users.delete',
              'products.read', 'products.write', 'products.delete',
              'categories.read', 'categories.write', 'categories.delete',
              'orders.read', 'orders.write', 'orders.delete',
              'banners.read', 'banners.write', 'banners.delete',
              'stations.read', 'stations.write', 'stations.delete',
              'analytics.read', 'settings.read', 'settings.write'
            ],
            is_active: true,
            last_login: new Date().toISOString()
          }
        }
        return null
      }

      if (!data || data.length === 0) {
        return null
      }

      const adminData = data[0]
      return {
        id: adminData.user_id,
        email: adminData.email,
        role_name: adminData.role_name,
        permissions: adminData.permissions || [],
        is_active: adminData.is_active,
        last_login: adminData.last_login
      }
    } catch (error) {
      console.error('Error in fetchAdminUser:', error)
      return null
    }
  }

  // Refresh user data
  const refreshUser = async () => {
    const { data: { session } } = await supabase.auth.getSession()
    if (session?.user) {
      const adminUser = await fetchAdminUser(session.user.id)
      setUser(adminUser)
    } else {
      setUser(null)
    }
  }

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Get initial session
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Error getting session:', error)
          setLoading(false)
          return
        }

        setSession(session)

        if (session?.user) {
          const adminUser = await fetchAdminUser(session.user.id)
          setUser(adminUser)
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
      } finally {
        setLoading(false)
      }
    }

    initializeAuth()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (_event, session) => {
        setSession(session)
        
        if (session?.user) {
          const adminUser = await fetchAdminUser(session.user.id)
          setUser(adminUser)
        } else {
          setUser(null)
        }
        
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true)
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        return { error }
      }

      if (data.user) {
        // Check if user is admin
        const adminUser = await fetchAdminUser(data.user.id)
        if (!adminUser) {
          // User exists but is not an admin
          await supabase.auth.signOut()
          return { error: { message: 'Bạn không có quyền truy cập vào trang quản trị' } }
        }

        if (!adminUser.is_active) {
          // Admin user is deactivated
          await supabase.auth.signOut()
          return { error: { message: 'Tài khoản quản trị của bạn đã bị vô hiệu hóa' } }
        }

        setUser(adminUser)
        setSession(data.session)
      }

      return { error: null }
    } catch (error) {
      console.error('Sign in error:', error)
      return { error: { message: 'Đã xảy ra lỗi khi đăng nhập' } }
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    try {
      setLoading(true)
      await supabase.auth.signOut()
      setUser(null)
      setSession(null)
    } catch (error) {
      console.error('Sign out error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <AdminAuthContext.Provider value={{
      user,
      session,
      loading,
      signIn,
      signOut,
      hasPermission,
      isAdmin,
      refreshUser,
    }}>
      {children}
    </AdminAuthContext.Provider>
  )
}

export function useAdminAuth() {
  const context = useContext(AdminAuthContext)
  if (!context) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider')
  }
  return context
}

// Higher-order component for protecting admin routes
export function withAdminAuth<P extends object>(
  Component: React.ComponentType<P>,
  requiredPermission?: string
) {
  return function ProtectedComponent(props: P) {
    const { user, loading, hasPermission } = useAdminAuth()

    if (loading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Đang tải...</p>
          </div>
        </div>
      )
    }

    if (!user) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Truy cập bị từ chối</h1>
            <p className="text-muted-foreground">Bạn cần đăng nhập để truy cập trang này.</p>
          </div>
        </div>
      )
    }

    if (requiredPermission && !hasPermission(requiredPermission)) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Không có quyền truy cập</h1>
            <p className="text-muted-foreground">
              Bạn không có quyền truy cập vào tính năng này.
            </p>
          </div>
        </div>
      )
    }

    return <Component {...props} />
  }
}
