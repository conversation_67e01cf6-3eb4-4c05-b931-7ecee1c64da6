import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import fs from 'fs'
import path from 'path'

// Mock data fallback
function getMockProduct(id: string) {
  try {
    const mockProductsPath = path.join(process.cwd(), '..', 'api', 'mock-output', 'products.json')
    const mockCategoriesPath = path.join(process.cwd(), '..', 'api', 'mock-output', 'categories.json')

    const productsData = fs.readFileSync(mockProductsPath, 'utf8')
    const categoriesData = fs.readFileSync(mockCategoriesPath, 'utf8')

    const products = JSON.parse(productsData)
    const categories = JSON.parse(categoriesData)

    const product = products.find((p: any) => p.id === parseInt(id))
    if (!product) return null

    return {
      ...product,
      category: categories.find((cat: any) => cat.id === product.categoryId)
    }
  } catch (error) {
    console.error('Error loading mock product:', error)
    return null
  }
}

export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Try Supabase first
    try {
      const { data: product, error } = await supabase
        .from('products')
        .select(`
          *,
          categories (
            id,
            name,
            image
          )
        `)
        .eq('id', params.id)
        .single()

      if (!error && product) {
        // Transform data to match the expected format
        const transformedProduct = {
          id: product.id,
          categoryId: product.category_id,
          name: product.name,
          price: product.price,
          originalPrice: product.original_price,
          image: product.image,
          detail: product.detail,
          category: product.categories
        }

        return NextResponse.json(transformedProduct)
      }
    } catch (supabaseError) {
      console.log('Supabase not available, using mock data')
    }

    // Fallback to mock data
    const product = getMockProduct(params.id)

    if (!product) {
      return NextResponse.json(
        { error: 'Không tìm thấy sản phẩm' },
        { status: 404 }
      )
    }

    return NextResponse.json(product)
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Lỗi hệ thống' },
      { status: 500 }
    )
  }
}
