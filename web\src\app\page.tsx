"use client"

import { useEffect, useState } from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { BannerCarousel } from "@/components/home/<USER>"
import { CategoryGrid } from "@/components/home/<USER>"
import { FeaturedProducts } from "@/components/home/<USER>"
import { Category } from "@/lib/supabase"

interface Product {
  id: number
  name: string
  price: number
  originalPrice: number
  image: string
  detail?: string
}

export default function Home() {
  const [banners, setBanners] = useState<string[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch banners
        const bannersResponse = await fetch('/api/banners')
        if (bannersResponse.ok) {
          const bannersData = await bannersResponse.json()
          setBanners(bannersData)
        }

        // Fetch categories
        const categoriesResponse = await fetch('/api/categories')
        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json()
          setCategories(categoriesData)
        }

        // Fetch featured products (first 10 products)
        const productsResponse = await fetch('/api/products?limit=10')
        if (productsResponse.ok) {
          const productsData = await productsResponse.json()
          setFeaturedProducts(productsData)
        }
      } catch (error) {
        console.error('Error fetching data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])



  if (loading) {
    return (
      <MainLayout>
        <div className="py-8">
          <div className="text-center">
            <p className="text-muted-foreground">Đang tải...</p>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="py-8 space-y-12">
        {/* Hero Banner */}
        <section>
          <BannerCarousel banners={banners} />
        </section>

        {/* Welcome Section */}
        <section className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-primary">
            Chào mừng đến với SachMart
          </h1>
          <p className="text-xl text-muted-foreground">
            Siêu thị trực tuyến hàng đầu Việt Nam
          </p>
          <p className="text-lg">
            Mua sắm thực phẩm tươi ngon, tiện lợi và nhanh chóng
          </p>
        </section>

        {/* Features */}
        <section className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center p-6 border rounded-lg">
            <h3 className="text-xl font-semibold mb-2">🥬 Tươi ngon</h3>
            <p className="text-muted-foreground">
              Sản phẩm tươi ngon được tuyển chọn kỹ càng
            </p>
          </div>
          <div className="text-center p-6 border rounded-lg">
            <h3 className="text-xl font-semibold mb-2">🚚 Giao hàng nhanh</h3>
            <p className="text-muted-foreground">
              Giao hàng trong ngày tại TP.HCM
            </p>
          </div>
          <div className="text-center p-6 border rounded-lg">
            <h3 className="text-xl font-semibold mb-2">💰 Giá tốt</h3>
            <p className="text-muted-foreground">
              Cam kết giá tốt nhất thị trường
            </p>
          </div>
        </section>

        {/* Categories */}
        <section>
          <CategoryGrid categories={categories} />
        </section>

        {/* Featured Products */}
        <section>
          <FeaturedProducts
            products={featuredProducts}
          />
        </section>
      </div>
    </MainLayout>
  )
}
